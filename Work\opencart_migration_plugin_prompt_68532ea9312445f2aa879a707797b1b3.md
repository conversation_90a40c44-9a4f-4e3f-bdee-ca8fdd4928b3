# Създаване на плъгин за миграция на данни в OpenCart 3

## Обща задача
Създай професионален плъгин за OpenCart 3.x, който позволява миграция на данни от външна база данни към текущата OpenCart инсталация. Плъгинът трябва да бъде надежден, сигурен и лесен за използване от администратори на магазини.

## Технически изисквания

### Основна архитектура
- **Платформа**: OpenCart 3.0.x и по-нови версии
- **PHP версия**: Съвместимост с PHP 7.4+
- **База данни**: MySQL 5.7+ / MariaDB 10.2+
- **Структура**: Следвай архитектурата на темата на Rakla.bg
- **Namespace**: Използвай подходящи namespaces за избягване на конфликти

### Файлова структура
```
theme/Backend/
├── Controller/Extension/Module/DataMigrator.php
├── Model/Extension/Module/DataMigrator.php
├── View/Template/extension/module/data_migrator.twig
theme/
├── Helper/
│   ├── DatabaseConnector.php
│   ├── DataMapper.php
│   ├── MigrationValidator.php
│   └── ProgressTracker.php
```

## Функционални изисквания

### 1. Картографиране на данни
- **Автоматично откриване**: Сканиране на структурата на външната (втора) БД
- **Ръчно картографиране**: Възможност за персонализиране на mapping-а
- **Поддържани обекти**:
  - Продукти (products)
  - Категории (categories)
  - Клиенти (customers)
  - Поръчки (orders)
  - Бързи поръчки (quick orders)
  - Производители (manufacturers)
  - Отзиви (reviews)
  - Куриери (delivery)
  - Начини за плащане (payment)
  - Атрибути (attributes)
  - Опции (options)
  - Изображения (images)
  - SEO url (seo_url)

### 3. Процес на миграция
- **Batch обработка**: Миграция на части за избягване на timeout, управление на паметта - освобождаване на паметта
- **Progress tracking**: Реално време показване на напредъка
- **Error handling**: Детайлно логване на грешки
- **Resume функция**: Възможност за продължаване при прекъсване
- **Rollback опция**: Възстановяване при неуспешна миграция

### 4. Валидация и трансформация
- **Data validation**: Проверка на интегритета на данните
- **Format conversion**: Автоматично конвертиране на формати
- **Duplicate detection**: Откриване и обработка на дублирани записи
- **Data cleaning**: Почистване на невалидни символи и форматиране

## Интерфейс и потребителски опит

### 1. Dashboard
- **Преглед на статуса**: Текущо състояние на миграциите
- **Бърз достъп**: Бутони за често използвани функции
- **Статистики**: Брой мигрирани записи, грешки, време

### 2. Wizard интерфейс
- **Стъпка 1**: Избор на данни за миграция
- **Стъпка 2**: Картографиране на полета
- **Стъпка 3**: Настройки за миграцията
- **Стъпка 4**: Преглед и стартиране

### 3. Мониториране
- **Real-time progress bar**: Визуален индикатор за напредъка
- **Detailed logs**: Подробни логове с възможност за филтриране
- **Error reports**: Отчети за грешки с препоръки за решаване

## Безопасност и валидация

### 1. Сигурност на данните
- **Access control**: Ограничаване на достъпа до администратори - само за разработчика чрез функцията isDeveloper()
- **Audit trail**: Логване на всички действия
- **Backup creation**: Автоматично създаване на backup преди миграция

### 2. Валидация на входните данни
- **SQL injection protection**: Защита срещу SQL инжекции
- **Input sanitization**: Почистване на входните данни
- **Type validation**: Проверка на типовете данни
- **Range validation**: Проверка на стойностите в допустими граници

### 3. Error handling
- **Graceful degradation**: Елегантно обработване на грешки
- **Detailed error messages**: Ясни съобщения за грешки
- **Recovery mechanisms**: Механизми за възстановяване
- **Notification system**: Уведомления за критични грешки

## Конфигурационни опции

### 1. Общи настройки
- **Batch size**: Размер на партидите за обработка
- **Timeout settings**: Настройки за timeout
- **Memory limits**: Ограничения за паметта
- **Retry attempts**: Брой опити при грешка

### 2. Специфични настройки
- **Image handling**: Как да се обработват изображенията
- **URL rewriting**: Пренаписване на URL адреси
- **SEO preservation**: Запазване на SEO данни
- **Multi-language support**: Поддръжка на многоезичност

## Документация и помощ

### 1. Потребителска документация
- **Installation guide**: Ръководство за инсталация
- **Configuration manual**: Ръководство за конфигурация
- **Troubleshooting guide**: Ръководство за отстраняване на проблеми
- **FAQ section**: Често задавани въпроси

### 2. Техническа документация
- **Code comments**: Подробни коментари в кода
- **Extension points**: Възможности за разширение

## Тестване и качество

### 1. Unit тестове
- **Integration testing**: Интеграционни тестове

### 2. Качество на кода
- **PSR standards**: Следване на PSR стандарти
- **Code documentation**: Документиране на кода
- **Performance optimization**: Оптимизация на производителността
- **Memory management**: Управление на паметта

## Изисквания за изпълнение

1. **Създай пълна файлова структура** на плъгина
2. **Имплементирай всички основни функции** описани по-горе
3. **Добави подробни коментари** в кода на български
4. **Създай примерни конфигурационни файлове**
5. **Включи SQL скриптове** за инсталация/деинсталация
6. **Добави error handling** за всички възможни сценарии
7. **Създай потребителски интерфейс** с Тailwind CSS, виж как са реализирани в другите модули на Rakla.bg
8. **Имплементирай AJAX функционалност** за real-time updates
9. **Добави валидация** на всички входни данни
10. **Създай документация** за инсталация и използване

## Допълнителни изисквания

- **Responsive design**: Адаптивен дизайн за мобилни устройства
- **Performance monitoring**: Мониториране на производителността
- **Extensibility**: Възможност за разширение с hooks и events


# 📘 Правила за работа на AI агента

## 🗣️ Език
1. **Винаги отговаряй на български език.**

## 💻 Основни технологии
2. **Основният език за програмиране е PHP.**
3. **При работа с JavaScript използвай само чист (vanilla) JavaScript**, освен ако не е изрично поискано друго.
4. **Работа с MySQL се извършва чрез PHPMyAdmin.**

## 🛠️ Работа с код и промени
6. **При изпълнение на задача за добавяне или промяна на код:**
   - Не редактирай, премахвай или подобрявай други части на кода, освен ако не е изрично указано.
   - Ако откриеш възможна грешка или подобрение, предложи го отделно, **без да го прилагаш в работния код.**
   - Винаги използвай is_callable вместо method_exists

7. **Винаги използвай директна кирилица.** Не използвай Unicode escape формати (напр. `\u041f` е грешно).

## 🗄️ Работа с бази данни

### 📌 Общи насоки:
- Използвай **нови редове** за по-добра четимост на SQL кода.
- Използвай **кавички** за SQL заявки и синтаксис `{$variable}` за променливи.

### 📌 OpenCart:
- Използвай константата `DB_PREFIX` за префикс на таблиците.

## 🧩 Организация на кода
9. **При генериране на по-сложна логика, я разбивай на подметоди (функции/методи)** за по-добра четимост и поддръжка.
10. **Ако файлът е голям, прави промените на по-малки стъпки**, заради ограничения в размера на файловете.
11. **Следвай принципа DRY (Don't Repeat Yourself).**
12. **Придържай се към добрите практики в разработката** – цел: поддържан, стабилен и четим код.
13. **Използвай съкратения синтаксис за масиви:**
    ```php
// Добре:
    $data = ['key' => 'valaue'];

    // Не:
    $data = array('key' => 'value');
```

## 🛡️ Архивиране и документация
14. **Ако в основната директория има файл `instrukcii-i-pravila.md`, прочети го.**
15. **Ако в основната директория има файл `project-conventions.md`, прочети го**, за да разбереш структурата и конвенциите на проекта.

## 📋 Организация на задачите
16. **Винаги организирай работата си чрез списък със задачи (task-ове)** – за яснота и за да не се пропусне нищо.
