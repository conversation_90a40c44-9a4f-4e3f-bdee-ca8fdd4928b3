
# Подкана за AI агент: Създаване на плъгин за миграция на данни в OpenCart 3

## Обща задача
Създай професионален плъгин за OpenCart 3.x, който позволява миграция на данни от външна база данни към текущата OpenCart инсталация. Плъгинът трябва да бъде надежден, сигурен и лесен за използване от администратори на магазини.

## Технически изисквания

### Основна архитектура
- **Платформа**: OpenCart 3.0.x и по-нови версии
- **PHP версия**: Съвместимост с PHP 7.4+ и PHP 8.x
- **База данни**: MySQL 5.7+ / MariaDB 10.2+
- **Структура**: Следвай OpenCart MVC архитектурата
- **Namespace**: Използвай подходящи namespaces за избягване на конфликти

### Файлова структура
```
admin/
├── controller/extension/module/data_migrator.php
├── model/extension/module/data_migrator.php
├── view/template/extension/module/data_migrator.twig
├── language/en-gb/extension/module/data_migrator.php
├── language/bg-bg/extension/module/data_migrator.php
system/
├── library/migrator/
│   ├── DatabaseConnector.php
│   ├── DataMapper.php
│   ├── MigrationValidator.php
│   └── ProgressTracker.php
```

## Функционални изисквания

### 1. Конфигурация на връзката
- **Поддържани бази данни**: MySQL, PostgreSQL, SQLite, SQL Server
- **Параметри за връзка**: хост, порт, потребител, парола, име на БД
- **Тест на връзката**: Валидация преди започване на миграцията
- **SSL поддръжка**: Опция за сигурни връзки

### 2. Картографиране на данни
- **Автоматично откриване**: Сканиране на структурата на външната БД
- **Ръчно картографиране**: Възможност за персонализиране на mapping-а
- **Поддържани обекти**:
  - Продукти (products)
  - Категории (categories)
  - Клиенти (customers)
  - Поръчки (orders)
  - Производители (manufacturers)
  - Атрибути (attributes)
  - Опции (options)
  - Изображения (images)

### 3. Процес на миграция
- **Batch обработка**: Миграция на части за избягване на timeout
- **Progress tracking**: Реално време показване на напредъка
- **Error handling**: Детайлно логване на грешки
- **Resume функция**: Възможност за продължаване при прекъсване
- **Rollback опция**: Възстановяване при неуспешна миграция

### 4. Валидация и трансформация
- **Data validation**: Проверка на интегритета на данните
- **Format conversion**: Автоматично конвертиране на формати
- **Duplicate detection**: Откриване и обработка на дублирани записи
- **Data cleaning**: Почистване на невалидни символи и форматиране

## Интерфейс и потребителски опит

### 1. Dashboard
- **Преглед на статуса**: Текущо състояние на миграциите
- **Бърз достъп**: Бутони за често използвани функции
- **Статистики**: Брой мигрирани записи, грешки, време

### 2. Wizard интерфейс
- **Стъпка 1**: Конфигурация на връзката
- **Стъпка 2**: Избор на данни за миграция
- **Стъпка 3**: Картографиране на полета
- **Стъпка 4**: Настройки за миграцията
- **Стъпка 5**: Преглед и стартиране

### 3. Мониториране
- **Real-time progress bar**: Визуален индикатор за напредъка
- **Detailed logs**: Подробни логове с възможност за филтриране
- **Error reports**: Отчети за грешки с препоръки за решаване

## Безопасност и валидация

### 1. Сигурност на данните
- **Encryption**: Криптиране на чувствителни данни
- **Access control**: Ограничаване на достъпа до администратори
- **Audit trail**: Логване на всички действия
- **Backup creation**: Автоматично създаване на backup преди миграция

### 2. Валидация на входните данни
- **SQL injection protection**: Защита срещу SQL инжекции
- **Input sanitization**: Почистване на входните данни
- **Type validation**: Проверка на типовете данни
- **Range validation**: Проверка на стойностите в допустими граници

### 3. Error handling
- **Graceful degradation**: Елегантно обработване на грешки
- **Detailed error messages**: Ясни съобщения за грешки
- **Recovery mechanisms**: Механизми за възстановяване
- **Notification system**: Уведомления за критични грешки

## Конфигурационни опции

### 1. Общи настройки
- **Batch size**: Размер на партидите за обработка
- **Timeout settings**: Настройки за timeout
- **Memory limits**: Ограничения за паметта
- **Retry attempts**: Брой опити при грешка

### 2. Специфични настройки
- **Image handling**: Как да се обработват изображенията
- **URL rewriting**: Пренаписване на URL адреси
- **SEO preservation**: Запазване на SEO данни
- **Multi-language support**: Поддръжка на многоезичност

## Документация и помощ

### 1. Потребителска документация
- **Installation guide**: Ръководство за инсталация
- **Configuration manual**: Ръководство за конфигурация
- **Troubleshooting guide**: Ръководство за отстраняване на проблеми
- **FAQ section**: Често задавани въпроси

### 2. Техническа документация
- **API documentation**: Документация на API
- **Database schema**: Схема на базата данни
- **Code comments**: Подробни коментари в кода
- **Extension points**: Възможности за разширение

## Тестване и качество

### 1. Unit тестове
- **Model testing**: Тестване на модели
- **Controller testing**: Тестване на контролери
- **Library testing**: Тестване на библиотеки
- **Integration testing**: Интеграционни тестове

### 2. Качество на кода
- **PSR standards**: Следване на PSR стандарти
- **Code documentation**: Документиране на кода
- **Performance optimization**: Оптимизация на производителността
- **Memory management**: Управление на паметта

## Примери за използване

### 1. Миграция от WooCommerce
- Картографиране на продукти и категории
- Трансформация на мета данни
- Обработка на вариации на продукти

### 2. Миграция от Magento
- Конвертиране на атрибути
- Обработка на сложни продуктови структури
- Миграция на клиентски данни

### 3. Миграция от custom система
- Гъвкаво картографиране на полета
- Трансформация на данни
- Валидация на специфични изисквания

## Изисквания за изпълнение

1. **Създай пълна файлова структура** на плъгина
2. **Имплементирай всички основни функции** описани по-горе
3. **Добави подробни коментари** в кода на български и английски
4. **Създай примерни конфигурационни файлове**
5. **Включи SQL скриптове** за инсталация/деинсталация
6. **Добави error handling** за всички възможни сценарии
7. **Създай потребителски интерфейс** с Bootstrap/OpenCart стилове
8. **Имплементирай AJAX функционалност** за real-time updates
9. **Добави валидация** на всички входни данни
10. **Създай документация** за инсталация и използване

## Допълнителни изисквания

- **Многоезична поддръжка**: Български и английски език
- **Responsive design**: Адаптивен дизайн за мобилни устройства
- **Performance monitoring**: Мониториране на производителността
- **Extensibility**: Възможност за разширение с hooks и events
- **Compatibility**: Тестване със стандартни OpenCart теми
