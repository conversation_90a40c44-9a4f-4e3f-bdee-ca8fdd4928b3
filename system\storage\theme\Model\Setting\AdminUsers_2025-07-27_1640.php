<?php

namespace Theme25\Model\Setting;

/**
 * Модел за настройки на администраторски потребители
 * Използва втората база данни за съхранение на данни
 */
class AdminUsers extends \Theme25\Model\Setting
{

    public function __construct($registry)
    {
        parent::__construct($registry);
        $this->settingPrefix = 'admin_users';
    }

    /**
     * Получава всички настройки за администраторски потребители
     * 
     * @return array
     */
    public function getSettings()
    {

        $settings = $this->getMultipleSettingsFromSecondDB([
            'session_timeout' => 3600,
            'max_login_attempts' => 5,
            'lockout_duration' => 900,
            'password_min_length' => 8,
            'password_require_uppercase' => 1,
            'password_require_lowercase' => 1,
            'password_require_numbers' => 1,
            'password_require_symbols' => 0,
            'password_expiry_days' => 90,
            'two_factor_enabled' => 0,
            'login_history_enabled' => 1,
            'login_history_days' => 30,
            'auto_logout_enabled' => 1,
            'concurrent_sessions_limit' => 1,
            'ip_whitelist_enabled' => 0,
            'ip_whitelist' => '',
            'email_notifications_enabled' => 1,
            'failed_login_notifications' => 1,
            'new_user_notifications' => 1,
            'role_change_notifications' => 1
        ]);

        return [
            // Сесии и сигурност
            'session_timeout' => $settings['session_timeout'],
            'max_login_attempts' => $settings['max_login_attempts'],
            'lockout_duration' => $settings['lockout_duration'],
            'auto_logout_enabled' => $settings['auto_logout_enabled'],
            'concurrent_sessions_limit' => $settings['concurrent_sessions_limit'],

            // Парола политики
            'password_min_length' => $settings['password_min_length'],
            'password_require_uppercase' => $settings['password_require_uppercase'],
            'password_require_lowercase' => $settings['password_require_lowercase'],
            'password_require_numbers' => $settings['password_require_numbers'],
            'password_require_symbols' => $settings['password_require_symbols'],
            'password_expiry_days' => $settings['password_expiry_days'],

            // Двуфакторна автентикация
            'two_factor_enabled' => $settings['two_factor_enabled'],

            // История на влизания
            'login_history_enabled' => $settings['login_history_enabled'],
            'login_history_days' => $settings['login_history_days'],

            // IP ограничения
            'ip_whitelist_enabled' => $settings['ip_whitelist_enabled'],
            'ip_whitelist' => $settings['ip_whitelist'],

            // Известия
            'email_notifications_enabled' => $settings['email_notifications_enabled'],
            'failed_login_notifications' => $settings['failed_login_notifications'],
            'new_user_notifications' => $settings['new_user_notifications'],
            'role_change_notifications' => $settings['role_change_notifications']
        ];
    }

    /**
     * Получава настройки от втората база данни
     * 
     * @param array $settings_array Масив с ключове и стойности по подразбиране
     * @return array
     */
    private function getMultipleSettingsFromSecondDB($settings_array)
    {
        $settings = [];

        foreach ($settings_array as $key => $default) {
            $settings[$key] = $this->getSettingFromSecondDB($key, $default);
        }

        return $settings;
    }

    /**
     * Запазва настройките за администраторски потребители
     * 
     * @param array $data Данни за запазване
     * @return bool|array True при успех, масив с грешки при неуспех
     */
    public function saveSettings($data)
    {

        // Валидация на данните
        $errors = $this->validateSettings($data);
        if (!empty($errors)) {
            return $errors;
        }

        // Подготовка на данните за запазване
        $settingsToSave = [
            'session_timeout' => (int) ($data['session_timeout'] ?? 3600),
            'max_login_attempts' => (int) ($data['max_login_attempts'] ?? 5),
            'lockout_duration' => (int) ($data['lockout_duration'] ?? 900),
            'password_min_length' => (int) ($data['password_min_length'] ?? 8),
            'password_require_uppercase' => isset($data['password_require_uppercase']) ? (int) $data['password_require_uppercase'] : 1,
            'password_require_lowercase' => isset($data['password_require_lowercase']) ? (int) $data['password_require_lowercase'] : 1,
            'password_require_numbers' => isset($data['password_require_numbers']) ? (int) $data['password_require_numbers'] : 1,
            'password_require_symbols' => isset($data['password_require_symbols']) ? (int) $data['password_require_symbols'] : 0,
            'password_expiry_days' => (int) ($data['password_expiry_days'] ?? 90),
            'two_factor_enabled' => isset($data['two_factor_enabled']) ? (int) $data['two_factor_enabled'] : 0,
            'login_history_enabled' => isset($data['login_history_enabled']) ? (int) $data['login_history_enabled'] : 1,
            'login_history_days' => (int) ($data['login_history_days'] ?? 30),
            'auto_logout_enabled' => isset($data['auto_logout_enabled']) ? (int) $data['auto_logout_enabled'] : 1,
            'concurrent_sessions_limit' => (int) ($data['concurrent_sessions_limit'] ?? 1),
            'ip_whitelist_enabled' => isset($data['ip_whitelist_enabled']) ? (int) $data['ip_whitelist_enabled'] : 0,
            'ip_whitelist' => $data['ip_whitelist'] ?? '',
            'email_notifications_enabled' => isset($data['email_notifications_enabled']) ? (int) $data['email_notifications_enabled'] : 1,
            'failed_login_notifications' => isset($data['failed_login_notifications']) ? (int) $data['failed_login_notifications'] : 1,
            'new_user_notifications' => isset($data['new_user_notifications']) ? (int) $data['new_user_notifications'] : 1,
            'role_change_notifications' => isset($data['role_change_notifications']) ? (int) $data['role_change_notifications'] : 1
        ];

        // Запазване на настройките във втората база данни
        return $this->setMultipleSettingsIntoSecondDB($settingsToSave);
    }

    /**
     * Валидира настройките за администраторски потребители
     * 
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    protected function validateSettings($data)
    {
        $errors = [];

        // Валидация на session timeout
        if (isset($data['session_timeout'])) {
            $sessionTimeout = (int) $data['session_timeout'];
            if ($sessionTimeout < 300 || $sessionTimeout > 86400) {
                $errors['session_timeout'] = 'Времето за сесия трябва да бъде между 5 минути и 24 часа';
            }
        }

        // Валидация на максимални опити за влизане
        if (isset($data['max_login_attempts'])) {
            $maxAttempts = (int) $data['max_login_attempts'];
            if ($maxAttempts < 1 || $maxAttempts > 20) {
                $errors['max_login_attempts'] = 'Максималните опити за влизане трябва да бъдат между 1 и 20';
            }
        }

        // Валидация на времето за блокиране
        if (isset($data['lockout_duration'])) {
            $lockoutDuration = (int) $data['lockout_duration'];
            if ($lockoutDuration < 60 || $lockoutDuration > 3600) {
                $errors['lockout_duration'] = 'Времето за блокиране трябва да бъде между 1 минута и 1 час';
            }
        }

        // Валидация на минимална дължина на паролата
        if (isset($data['password_min_length'])) {
            $minLength = (int) $data['password_min_length'];
            if ($minLength < 6 || $minLength > 50) {
                $errors['password_min_length'] = 'Минималната дължина на паролата трябва да бъде между 6 и 50 символа';
            }
        }

        // Валидация на дни за изтичане на паролата
        if (isset($data['password_expiry_days'])) {
            $expiryDays = (int) $data['password_expiry_days'];
            if ($expiryDays < 0 || $expiryDays > 365) {
                $errors['password_expiry_days'] = 'Дните за изтичане на паролата трябва да бъдат между 0 и 365';
            }
        }

        // Валидация на дни за история на влизания
        if (isset($data['login_history_days'])) {
            $historyDays = (int) $data['login_history_days'];
            if ($historyDays < 1 || $historyDays > 365) {
                $errors['login_history_days'] = 'Дните за история на влизания трябва да бъдат между 1 и 365';
            }
        }

        // Валидация на лимит за едновременни сесии
        if (isset($data['concurrent_sessions_limit'])) {
            $sessionsLimit = (int) $data['concurrent_sessions_limit'];
            if ($sessionsLimit < 1 || $sessionsLimit > 10) {
                $errors['concurrent_sessions_limit'] = 'Лимитът за едновременни сесии трябва да бъде между 1 и 10';
            }
        }

        // Валидация на IP whitelist
        if (!empty($data['ip_whitelist'])) {
            $ipList = explode(',', $data['ip_whitelist']);
            foreach ($ipList as $ip) {
                $ip = trim($ip);
                if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
                    $errors['ip_whitelist'] = 'Невалиден IP адрес в списъка: ' . $ip;
                    break;
                }
            }
        }

        return $errors;
    }

    /**
     * Получава статистики за администраторските потребители
     * 
     * @return array
     */
    public function getAdminUsersStatistics()
    {
        try {
            // Тук може да се добави логика за получаване на статистики от втората база данни
            return [
                'total_admin_users' => 0,
                'active_sessions' => 0,
                'failed_logins_today' => 0,
                'locked_accounts' => 0,
                'password_expiring_soon' => 0
            ];
        } catch (\Exception $e) {
            return [
                'total_admin_users' => 0,
                'active_sessions' => 0,
                'failed_logins_today' => 0,
                'locked_accounts' => 0,
                'password_expiring_soon' => 0
            ];
        }
    }

    /**
     * Получава всички администраторски потребители
     * 
     * @return array
     */
    public function getAdminUsers()
    {
        try {
           
            $secondDb = $this->getSecondDB();


            if (!$secondDb) {
                // Ако няма връзка към втората база данни, връщаме празен масив
                return [];
            }

            // SQL заявка за получаване на администраторски потребители от втората база данни
            $sql = "SELECT 
                        u.user_id,
                        u.username,
                        u.firstname,
                        u.lastname,
                        u.email,
                        u.image,
                        u.status,
                        u.code,
                        u.date_added,
                        ug.name as user_group,
                        ug.user_group_id
                    FROM " . DB_PREFIX . "user u
                    LEFT JOIN " . DB_PREFIX . "user_group ug ON (u.user_group_id = ug.user_group_id)
                    ORDER BY u.username ASC";

            $query = $secondDb->query($sql);

            $users = [];

            if ($query->rows) {
                foreach ($query->rows as $row) {
                    $users[] = [
                        'user_id' => (int) $row['user_id'],
                        'username' => $row['username'],
                        'firstname' => $row['firstname'],
                        'lastname' => $row['lastname'],
                        'email' => $row['email'],
                        'user_group' => $row['user_group'] ?? 'Неопределена',
                        'user_group_id' => (int) ($row['user_group_id'] ?? 0),
                        'status' => (int) $row['status'],
                        'date_added' => $row['date_added'],
                        'image' => $row['image'] ?? '',
                        'code' => $row['code']
                    ];
                }
            }

            return $users;

        } catch (\Exception $e) {

            F()->log->developer("Грешка при получаване на администраторски потребители: " . $e->getMessage(), __FILE__, __LINE__);
            // Логване на грешката
            if (method_exists($this, 'log') && $this->log) {
                $this->log->error("Грешка при получаване на администраторски потребители: " . $e->getMessage());
            }

            return [];
        }
    }

    /**
     * Получава налични роли за администраторски потребители
     * 
     * @return array
     */
    public function getAvailableRoles()
    {
        try {
            $secondDb = $this->getSecondDB();

            if (!$secondDb) {
                // Ако няма връзка към втората база данни, връщаме стандартните роли
                return $this->getDefaultRoles();
            }

            // SQL заявка за получаване на потребителски групи от втората база данни
            $sql = "SELECT 
                        ug.user_group_id,
                        ug.name,
                        ug.permission
                    FROM " . DB_PREFIX . "user_group ug
                    ORDER BY ug.name ASC";

            $query = $secondDb->query($sql);

            $roles = [];

            if ($query->rows) {
                foreach ($query->rows as $row) {
                    $permissions = [];

                    // Обработка на разрешенията ако са в JSON формат
                    if (!empty($row['permission'])) {
                        $permissionData = json_decode($row['permission'], true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($permissionData)) {
                            $permissions = $permissionData;
                        } else {
                            // Ако не е JSON, третираме като обикновен текст
                            $permissions = $row['permission'];
                        }
                    }

                    $roles[] = [
                        'user_group_id' => (int) $row['user_group_id'],
                        'name' => $row['name'],
                        'permissions' => $permissions
                    ];
                }
            }


            return $roles;

        } catch (\Exception $e) {
            // Логване на грешката
            F()->log->developer("Грешка при получаване на роли: {$e->getMessage()}", __FILE__, __LINE__);

            if (method_exists($this, 'log') && $this->log) {
                $this->log->error("Грешка при получаване на роли: {$e->getMessage()}");
            }

            // При грешка връщаме стандартните роли
            return $this->getDefaultRoles();
        }
    }

    /**
     * Получава стандартните роли като резервен вариант
     * 
     * @return array
     */
    private function getDefaultRoles()
    {
        return [
            ['user_group_id' => 1, 'name' => 'Супер администратор', 'permissions' => '*'],
            ['user_group_id' => 2, 'name' => 'Администратор', 'permissions' => 'admin'],
            ['user_group_id' => 3, 'name' => 'Модератор', 'permissions' => 'moderate'],
            ['user_group_id' => 4, 'name' => 'Редактор', 'permissions' => 'edit']
        ];
    }

    /**
     * Добавяне на нов потребител
     */
    public function addUser($data) {

        $secondDb = $this->getSecondDB();

        try {
            $sql = "INSERT INTO " . DB_PREFIX . "user SET
                    username = '" . $secondDb->escape($data['username']) . "',
                    email = '" . $secondDb->escape($data['email']) . "',
                    firstname = '" . $secondDb->escape($data['firstname']) . "',
                    lastname = '" . $secondDb->escape($data['lastname']) . "',
                    user_group_id = '" . (int)$data['user_group_id'] . "',
                    password = '" . $secondDb->escape($data['password']) . "',
                    status = '" . (int)$data['status'] . "',
                    date_added = NOW()";

            $secondDb->query($sql);
            return $secondDb->getLastId();

        } catch (Exception $e) {
            error_log('Error adding user: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Редактиране на потребител
     */
    public function editUser($userId, $data) {
        try {

            $secondDb = $this->getSecondDB();

            $set = [];
            
            if (isset($data['username'])) {
                $set[] = "username = '" . $secondDb->escape($data['username']) . "'";
            }
            if (isset($data['email'])) {
                $set[] = "email = '" . $secondDb->escape($data['email']) . "'";
            }
            if (isset($data['firstname'])) {
                $set[] = "firstname = '" . $secondDb->escape($data['firstname']) . "'";
            }
            if (isset($data['lastname'])) {
                $set[] = "lastname = '" . $secondDb->escape($data['lastname']) . "'";
            }
            if (isset($data['user_group_id'])) {
                $set[] = "user_group_id = '" . (int)$data['user_group_id'] . "'";
            }
            if (isset($data['status'])) {
                $set[] = "status = '" . (int)$data['status'] . "'";
            }
            
            if (empty($set)) {
                throw new \Exception('Не са посочени данни за актуализация');
            }
            
            $sql = "UPDATE " . DB_PREFIX . "user SET " . implode(", ", $set);

            if (!empty($data['password'])) {
                $sql .= ", password = '" . $secondDb->escape($data['password']) . "'";
            }

            $sql .= " WHERE user_id = '" . (int)$userId . "'";

            $secondDb->query($sql);
            return true;

        } catch (Exception $e) {
            error_log('Error editing user: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Изтриване на потребител
     */
    public function deleteUser($userId) {

        $secondDb = $this->getSecondDB();

        try {
            $secondDb->query("DELETE FROM " . DB_PREFIX . "user WHERE user_id = '" . (int)$userId . "'");
            return true;

        } catch (Exception $e) {
            error_log('Error deleting user: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на потребител по ID
     */
    public function getUser($userId) {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT u.*, ug.name as user_group
                                     FROM " . DB_PREFIX . "user u
                                     LEFT JOIN " . DB_PREFIX . "user_group ug ON (u.user_group_id = ug.user_group_id)
                                     WHERE u.user_id = '" . (int)$userId . "'");

            return $query->row;

        } catch (Exception $e) {
            error_log('Error getting user: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на потребител по потребителско име
     */
    public function getUserByUsername($username) {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT * FROM " . DB_PREFIX . "user WHERE username = '" . $secondDb->escape($username) . "'");
            return $query->row;

        } catch (Exception $e) {
            error_log('Error getting user by username: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на потребител по email
     */
    public function getUserByEmail($email) {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT * FROM " . DB_PREFIX . "user WHERE email = '" . $secondDb->escape($email) . "'");
            return $query->row;

        } catch (Exception $e) {
            error_log('Error getting user by email: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на потребителски групи
     */
    public function getUserGroups() {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT ug.*,
                                     (SELECT COUNT(*) FROM " . DB_PREFIX . "user u WHERE u.user_group_id = ug.user_group_id) as users_count
                                     FROM " . DB_PREFIX . "user_group ug
                                     ORDER BY ug.name");

            return $query->rows;

        } catch (Exception $e) {
            error_log('Error getting user groups: ' . $e->getMessage());
            return [];
        }
    }

    public function getUserGroup($user_group_id) {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT DISTINCT * FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");

            if ($query->row) {
                $user_group = [
                    'user_group_id' => $query->row['user_group_id'],
                    'name' => $query->row['name'],
                    'permission' => json_decode($query->row['permission'], true) ?: []
                ];
                return $user_group;
            }

            return [];

        } catch (Exception $e) {
            error_log('Error getting user group: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Добавяне на нова потребителска група
     */
    public function addUserGroup($data) {

        $secondDb = $this->getSecondDB();

        try {
            $secondDb->query("INSERT INTO " . DB_PREFIX . "user_group SET
                            name = '" . $secondDb->escape($data['name']) . "',
                            permission = '" . $secondDb->escape(json_encode($data['permission'])) . "'");

            return $secondDb->getLastId();

        } catch (Exception $e) {
            error_log('Error adding user group: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Редактиране на потребителска група
     */
    public function editUserGroup($groupId, $data) {

        $secondDb = $this->getSecondDB();
        
        try {
            $secondDb->query("UPDATE " . DB_PREFIX . "user_group SET
                            name = '" . $secondDb->escape($data['name']) . "',
                            permission = '" . $secondDb->escape(json_encode($data['permission'])) . "'
                            WHERE user_group_id = '" . (int)$groupId . "'");

            return true;

        } catch (Exception $e) {
            error_log('Error editing user group: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Изтриване на потребителска група
     */
    public function deleteUserGroup($groupId) {

        $secondDb = $this->getSecondDB();

        try {
            $secondDb->query("DELETE FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$groupId . "'");
            return true;

        } catch (Exception $e) {
            error_log('Error deleting user group: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на група по име
     */
    public function getUserGroupByName($name) {

        $secondDb = $this->getSecondDB();
        try {
            $query = $secondDb->query("SELECT * FROM " . DB_PREFIX . "user_group WHERE name = '" . $secondDb->escape($name) . "'");
            return $query->row;

        } catch (Exception $e) {
            error_log('Error getting user group by name: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Получаване на броя потребители в група
     */
    public function getUsersInGroup($groupId) {

        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "user WHERE user_group_id = '" . (int)$groupId . "'");
            return $query->row['total'];

        } catch (Exception $e) {
            error_log('Error getting users in group: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Получаване на всички налични права в системата
     */
    public function getAvailablePermissions() {
        // Дефиниране на всички налични права в системата
        $permissions = [
            'access' => [
                'catalog/category' => 'Категории',
                'catalog/product' => 'Продукти',
                'catalog/recurring' => 'Повтарящи се профили',
                'catalog/filter' => 'Филтри',
                'catalog/attribute' => 'Атрибути',
                'catalog/attribute_group' => 'Групи атрибути',
                'catalog/option' => 'Опции',
                'catalog/manufacturer' => 'Производители',
                'catalog/download' => 'Изтегляния',
                'catalog/review' => 'Отзиви',
                'catalog/information' => 'Информация',
                'extension/extension' => 'Разширения',
                'extension/modification' => 'Модификации',
                'extension/installer' => 'Инсталатор',
                'extension/event' => 'Събития',
                'extension/openbay' => 'OpenBay Pro',
                'design/layout' => 'Макети',
                'design/theme' => 'Редактор на теми',
                'design/translation' => 'Редактор на преводи',
                'design/banner' => 'Банери',
                'design/seo_url' => 'SEO URL',
                'sale/order' => 'Поръчки',
                'sale/recurring' => 'Повтарящи се поръчки',
                'sale/return' => 'Връщания',
                'sale/voucher' => 'Ваучери',
                'sale/voucher_theme' => 'Теми за ваучери',
                'sale/contact' => 'Поща',
                'sale/coupon' => 'Купони',
                'customer/customer' => 'Клиенти',
                'customer/customer_group' => 'Групи клиенти',
                'customer/customer_approval' => 'Одобрения на клиенти',
                'customer/custom_field' => 'Персонализирани полета',
                'marketing/marketing' => 'Маркетинг',
                'marketing/affiliate' => 'Партньори',
                'marketing/coupon' => 'Купони',
                'system/setting' => 'Настройки',
                'system/user' => 'Потребители',
                'system/user_group' => 'Групи потребители',
                'system/api' => 'API',
                'system/log' => 'Логове',
                'localisation/language' => 'Езици',
                'localisation/currency' => 'Валути',
                'localisation/stock_status' => 'Статуси на наличност',
                'localisation/order_status' => 'Статуси на поръчки',
                'localisation/return_status' => 'Статуси на връщания',
                'localisation/return_action' => 'Действия за връщания',
                'localisation/return_reason' => 'Причини за връщания',
                'localisation/country' => 'Държави',
                'localisation/zone' => 'Зони',
                'localisation/geo_zone' => 'Географски зони',
                'localisation/tax_class' => 'Данъчни класове',
                'localisation/tax_rate' => 'Данъчни ставки',
                'localisation/location' => 'Местоположения',
                'localisation/length_class' => 'Класове дължина',
                'localisation/weight_class' => 'Класове тегло',
                'tool/backup' => 'Резервно копие',
                'tool/upload' => 'Качвания',
                'tool/log' => 'Логове за грешки',
                'report/report' => 'Отчети',
                'report/online' => 'Онлайн отчети',
                'report/customer_activity' => 'Активност на клиенти',
                'report/customer_search' => 'Търсения на клиенти',
                'report/customer_transaction' => 'Транзакции на клиенти',
                'report/customer_reward' => 'Награди на клиенти',
                'report/customer_credit' => 'Кредити на клиенти',
                'report/affiliate_activity' => 'Активност на партньори',
                'report/affiliate_commission' => 'Комисионни на партньори',
                'report/product_viewed' => 'Преглеждани продукти',
                'report/product_purchased' => 'Закупени продукти',
                'report/sale_order' => 'Отчети за продажби',
                'report/sale_tax' => 'Данъчни отчети',
                'report/sale_shipping' => 'Отчети за доставка',
                'report/sale_return' => 'Отчети за връщания',
                'report/sale_coupon' => 'Отчети за купони',
                'report/marketing' => 'Маркетингови отчети'
            ],
            'modify' => [
                'catalog/category' => 'Категории',
                'catalog/product' => 'Продукти',
                'catalog/recurring' => 'Повтарящи се профили',
                'catalog/filter' => 'Филтри',
                'catalog/attribute' => 'Атрибути',
                'catalog/attribute_group' => 'Групи атрибути',
                'catalog/option' => 'Опции',
                'catalog/manufacturer' => 'Производители',
                'catalog/download' => 'Изтегляния',
                'catalog/review' => 'Отзиви',
                'catalog/information' => 'Информация',
                'extension/extension' => 'Разширения',
                'extension/modification' => 'Модификации',
                'extension/installer' => 'Инсталатор',
                'extension/event' => 'Събития',
                'extension/openbay' => 'OpenBay Pro',
                'design/layout' => 'Макети',
                'design/theme' => 'Редактор на теми',
                'design/translation' => 'Редактор на преводи',
                'design/banner' => 'Банери',
                'design/seo_url' => 'SEO URL',
                'sale/order' => 'Поръчки',
                'sale/recurring' => 'Повтарящи се поръчки',
                'sale/return' => 'Връщания',
                'sale/voucher' => 'Ваучери',
                'sale/voucher_theme' => 'Теми за ваучери',
                'sale/contact' => 'Поща',
                'sale/coupon' => 'Купони',
                'customer/customer' => 'Клиенти',
                'customer/customer_group' => 'Групи клиенти',
                'customer/customer_approval' => 'Одобрения на клиенти',
                'customer/custom_field' => 'Персонализирани полета',
                'marketing/marketing' => 'Маркетинг',
                'marketing/affiliate' => 'Партньори',
                'marketing/coupon' => 'Купони',
                'system/setting' => 'Настройки',
                'system/user' => 'Потребители',
                'system/user_group' => 'Групи потребители',
                'system/api' => 'API',
                'system/log' => 'Логове',
                'localisation/language' => 'Езици',
                'localisation/currency' => 'Валути',
                'localisation/stock_status' => 'Статуси на наличност',
                'localisation/order_status' => 'Статуси на поръчки',
                'localisation/return_status' => 'Статуси на връщания',
                'localisation/return_action' => 'Действия за връщания',
                'localisation/return_reason' => 'Причини за връщания',
                'localisation/country' => 'Държави',
                'localisation/zone' => 'Зони',
                'localisation/geo_zone' => 'Географски зони',
                'localisation/tax_class' => 'Данъчни класове',
                'localisation/tax_rate' => 'Данъчни ставки',
                'localisation/location' => 'Местоположения',
                'localisation/length_class' => 'Класове дължина',
                'localisation/weight_class' => 'Класове тегло',
                'tool/backup' => 'Резервно копие',
                'tool/upload' => 'Качвания',
                'tool/log' => 'Логове за грешки',
                'report/report' => 'Отчети',
                'report/online' => 'Онлайн отчети',
                'report/customer_activity' => 'Активност на клиенти',
                'report/customer_search' => 'Търсения на клиенти',
                'report/customer_transaction' => 'Транзакции на клиенти',
                'report/customer_reward' => 'Награди на клиенти',
                'report/customer_credit' => 'Кредити на клиенти',
                'report/affiliate_activity' => 'Активност на партньори',
                'report/affiliate_commission' => 'Комисионни на партньори',
                'report/product_viewed' => 'Преглеждани продукти',
                'report/product_purchased' => 'Закупени продукти',
                'report/sale_order' => 'Отчети за продажби',
                'report/sale_tax' => 'Данъчни отчети',
                'report/sale_shipping' => 'Отчети за доставка',
                'report/sale_return' => 'Отчети за връщания',
                'report/sale_coupon' => 'Отчети за купони',
                'report/marketing' => 'Маркетингови отчети'
            ]
        ];

        return $permissions;
    }

    /**
     * Получаване на правата на потребителска група
     */
    public function getUserGroupPermissions($groupId) {
        $secondDb = $this->getSecondDB();

        try {
            $query = $secondDb->query("SELECT permission FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$groupId . "'");

            if ($query->row && !empty($query->row['permission'])) {
                return json_decode($query->row['permission'], true) ?: [];
            }

            return [];

        } catch (Exception $e) {
            error_log('Error getting user group permissions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Редактиране на потребителска група с права
     */
    public function editUserGroupWithPermissions($groupId, $groupData, $permissions) {
        $secondDb = $this->getSecondDB();

        try {
            $secondDb->query("UPDATE " . DB_PREFIX . "user_group SET
                            name = '" . $secondDb->escape($groupData['name']) . "',
                            permission = '" . $secondDb->escape(json_encode($permissions)) . "'
                            WHERE user_group_id = '" . (int)$groupId . "'");

            return true;

        } catch (Exception $e) {
            error_log('Error editing user group with permissions: ' . $e->getMessage());
            return false;
        }
    }
}
