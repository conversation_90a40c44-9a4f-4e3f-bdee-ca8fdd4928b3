# Конвенции и архитектура на Rakla.bg проекта

## 📋 Съдържание
1. [Архитектура на контролери](#архитектура-на-контролери)
2. [Структура на основния контролер](#структура-на-основния-контролер)
3. [Организация на суб-контролерите](#организация-на-суб-контролерите)
4. [Принципи за разделяне на отговорностите](#принципи-за-разделяне-на-отговорностите)
5. [Практически примери](#практически-примери)
6. [Методология за структуриране](#методология-за-структуриране)
7. [Конвенции за именуване](#конвенции-за-именуване)
8. [Решени проблеми](#решени-проблеми)

---

## 🏗️ Архитектура на контролери

### Основни принципи
Rakla.bg проектът използва **dispatcher архитектура** за контролери, където:
- **Основният контролер** функционира като разпределител (dispatcher)
- **Суб-контролерите** съдържат цялата бизнес логика
- **Разделяне на отговорностите** по функционалност
- **Лесно разширяване** чрез нови суб-контролери

### Файлова структура
```
system/storage/theme/Backend/Controller/
├── Catalog/
│   └── Product/
│       ├── Product.php (основен dispatcher)
│       └── Product/
│           ├── Index.php (листване)
│           ├── Edit.php (редактиране)
│           ├── AttributeAutocomplete.php
│           ├── CategoryAutocomplete.php
│           └── ...
└── Common/
    └── ImageManager/
        ├── ImageManager.php (основен dispatcher)
        └── ImageManager/
            ├── Directory.php (навигация)
            ├── Upload.php (качване)
            └── Validation.php (валидация)
```

---

## 🎯 Структура на основния контролер

### Dispatcher методи
Основният контролер съдържа **само dispatcher методи**:

```php
<?php
namespace Theme25\Backend\Controller\Catalog;

class Product extends \Theme25\Controller {
    
    public function index() {
        $this->setTitle('Продукти');
        $this->initAdminData();
        
        $subController = $this->setBackendSubController('Catalog/Product/Index', $this);
        $subController->prepareData();
        
        $this->renderTemplateWithDataAndOutput('catalog/product');
    }
    
    public function edit() {
        $this->setTitle('Редакция на продукт');
        $this->initAdminData();
        
        $subController = $this->setBackendSubController('Catalog/Product/Edit', $this);
        $subController->prepareProductForm();
        
        $this->renderTemplateWithDataAndOutput('catalog/product_form');
    }
}
```

### Методи в основния контролер
**Остават в основния контролер:**
- **Action методи** (`index()`, `edit()`, `add()`) - само за инициализация и делегиране
- **AJAX endpoints** (`autocomplete()`) - за динамично зареждане на суб-контролери
- **Routing логика** - определяне кой суб-контролер да се използва

**НЕ остават в основния контролер:**
- Бизнес логика
- Обработка на данни
- Валидация
- Сложни операции

---

## 🔧 Организация на суб-контролерите

### Базов клас
Всички суб-контролери наследяват `\Theme25\ControllerSubMethods`:

```php
<?php
namespace Theme25\Backend\Controller\Catalog\Product;

class Index extends \Theme25\ControllerSubMethods {
    
    public function prepareData() {
        $this->prepareProductListData()
             ->prepareFilterOptions()
             ->prepareProductItems()
             ->preparePagination();
    }
    
    private function prepareProductListData() {
        // Бизнес логика
        return $this;
    }
}
```

### Конвенции за именуване
- **Класове**: PascalCase (`Index`, `Edit`, `AttributeAutocomplete`)
- **Namespace**: `Theme25\Backend\Controller\{Module}\{Controller}`
- **Методи**: camelCase (`prepareData()`, `loadContents()`)
- **Файлове**: PascalCase.php (`Index.php`, `Edit.php`)

### Създаване на суб-контролери
```php
// В основния контролер
$subController = $this->setBackendSubController('Catalog/Product/Index', $this);

// Метод в базовия Controller клас
public function setBackendSubController($subcontroller_path=null, $main_controller=null) {
    $instance_subcontroller = '\\Theme25\\Backend\\Controller\\'.str_replace('/', '\\', $subcontroller_path);
    
    if (!class_exists($instance_subcontroller)) {
        return false;
    }
    
    return new $instance_subcontroller($main_controller);
}
```

---

## 🎯 Принципи за разделяне на отговорностите

### Функционалности в суб-контролери

**1. По основна функционалност:**
- `Index.php` - листване, филтриране, пагинация
- `Edit.php` - редактиране, валидация, подготовка на форми
- `Add.php` - добавяне на нови записи

**2. По специализирани операции:**
- `*Autocomplete.php` - AJAX автодопълване
- `Directory.php` - навигация и листване на файлове
- `Upload.php` - качване на файлове
- `Validation.php` - валидация на данни

**3. По тип данни:**
- `AttributeAutocomplete.php` - автодопълване за атрибути
- `CategoryAutocomplete.php` - автодопълване за категории
- `ProductAutocomplete.php` - автодопълване за продукти

### Как се избягва удължаването
```php
// Вместо един голям метод
public function prepareData() {
    // 200+ реда код
}

// Разделяме на логически части
public function prepareData() {
    $this->prepareProductListData()
         ->prepareFilterOptions()
         ->prepareProductItems()
         ->preparePagination();
}

private function prepareProductListData() {
    // 20-30 реда
    return $this;
}

private function prepareFilterOptions() {
    // 20-30 реда
    return $this;
}
```

---

## 💡 Практически примери

### Пример 1: Динамично зареждане на суб-контролери
```php
public function autocomplete() {
    $json = [];
    
    if ($this->requestGet('type')) {
        $type = $this->requestGet('type');
        
        // Динамично зареждане на суб-контролер
        $sub_controller = $this->setBackendSubController('Catalog/Product/' . ucfirst($type) . 'Autocomplete', $this);
        
        if ($sub_controller && is_callable([$sub_controller, 'autocomplete'])) {
            $json = $sub_controller->autocomplete($this->requestGet());
        }
    }
    
    $this->setJSONResponseOutput($json);
}
```

### Пример 2: Комуникация между суб-контролери
```php
// В Directory.php
public function loadContents() {
    // Използваме друг суб-контролер за валидация
    $validationController = $this->setBackendSubController('Common/ImageManager/Validation', $this->_controller);
    $validation = $validationController->validateDirectoryAccess();
    
    if (!$validation['valid']) {
        return ['success' => false, 'error' => $validation['error']];
    }
    
    return $this->loadDirectoryContents($validation['directory']);
}
```

### Пример 3: Верижно извикване на методи
```php
public function prepareData() {
    $this->prepareProductListData()
         ->prepareFilterOptions()
         ->prepareProductItems()
         ->preparePagination();
         
    $this->setData([
        'back_url' => $this->getAdminLink('catalog/product')
    ]);
}
```

---

## 📐 Методология за структуриране

### Стъпки за създаване на нов контролер

**1. Създаване на основния контролер**
```php
<?php
namespace Theme25\Backend\Controller\{Module};

class {Controller} extends \Theme25\Controller {
    
    public function index() {
        $this->initAdminData();
        $subController = $this->setBackendSubController('{Module}/{Controller}/Index', $this);
        $result = $subController->prepareData();
        $this->renderTemplateWithDataAndOutput('{template}');
    }
}
```

**2. Създаване на суб-контролери**
```php
<?php
namespace Theme25\Backend\Controller\{Module}\{Controller};

class Index extends \Theme25\ControllerSubMethods {
    
    public function prepareData() {
        // Бизнес логика
        return $this;
    }
}
```

**3. Разделяне на отговорностите**
- Анализирай функционалностите
- Групирай по логически части
- Създай отделни суб-контролери
- Използвай верижно извикване

**4. Тестване и рефакториране**
- Тествай всеки суб-контролер независимо
- Рефакторирай при нужда
- Документирай промените

### Предимства на архитектурата

✅ **Разделяне на отговорностите** - всеки суб-контролер има ясна роля  
✅ **Лесно разширяване** - нови функционалности = нови суб-контролери  
✅ **Четимост** - кратък основен контролер, логично групирани методи  
✅ **Поддръжка** - промени в една функционалност не засягат други  
✅ **Тестване** - всеки суб-контролер може да се тества независимо  
✅ **Повторна употреба** - суб-контролери могат да се използват от други контролери  

---

### Стъпки за създаване на нов модел

**1. Създаване на модел**
```php
<?php
namespace Theme25\Backend\Model\{Module};

class {Module} extends \Theme25\Model {
    
    public function someMethod() {
        
    }
}
```
---

## 🏷️ Конвенции за именуване

### Файлове и директории
```
PascalCase.php за файлове
PascalCase/ за директории
```

### Класове и методи
```php
class ProductController          // PascalCase за класове
public function prepareData()    // camelCase за методи
private function loadItems()     // camelCase за private методи
```

### Namespace конвенции
```php
Theme25\Backend\Controller\{Module}\{Controller}\{SubController}
```

### Променливи и свойства
```php
$productData        // camelCase
$this->imageModel   // camelCase за свойства
```

---

## 🚀 Препоръки за бъдещо развитие

### 1. Нови контролери
При създаване на нови контролери:
- Започни с dispatcher архитектурата
- Раздели функционалностите в суб-контролери
- Следвай конвенциите за именуване
- Документирай промените

### 2. Рефакториране на съществуващи
За съществуващи контролери:
- Анализирай сложността (над 200 реда = кандидат за рефакториране)
- Идентифицирай логически групи
- Създай backup преди промени
- Тествай функционалността след промени

### 3. Тестване
- Всеки суб-контролер трябва да има unit тестове
- Интеграционни тестове за цялата функционалност
- Автоматизирани тестове за критични операции

### 4. Документация
- Актуализирай `project-conventions.md` при промени
- Документирай всички нови архитектурни решения
- Поддържай `completed-tasks.md` актуален

---

## 📝 Заключение

Dispatcher архитектурата на Rakla.bg проекта осигурява:
- **Отлична организация** на кода
- **Лесна поддръжка** и разширяване
- **Ясно разделяне** на отговорностите
- **Високо качество** на кода
- **Бърза разработка** на нови функционалности

Следването на тези конвенции гарантира консистентност и качество в целия проект.
