<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за сигурност
 *
 * Този контролер управлява логиката за показване и обработка на настройките за сигурност,
 * включително IP ограничения, двуфакторна автентикация и други мерки за сигурност.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Security extends \Theme25\ControllerSubMethods {

    /**
     * Подготвя данните за настройките за сигурност
     */
    public function prepareData() {
        $this->prepareSecuritySettingsData()
             ->prepareIPRestrictionData()
             ->prepareSessionData()
             ->preparePasswordPolicyData()
             ->prepareUrlsAndActions()
             ->prepareValidationRules();
    }

    /**
     * Подготвя основните данни за настройките за сигурност
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSecuritySettingsData() {
        try {
            $this->loadModelAs('setting/security', 'securitySettings');
            $settings = $this->securitySettings->getSettings();
            
            // Добавяне на настройките към данните
            foreach ($settings as $key => $value) {
                $this->setData($key, $value);
            }
            
        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на настройки за сигурност: ' . $e->getMessage());
            
            // Задаване на стойности по подразбиране
            $this->setData([
                'ip_restriction_enabled' => 0,
                'ip_blocking_enabled' => 0,
                'ip_whitelist' => '',
                'allowed_ips' => [],
                'session_timeout' => 3600,
                'force_ssl' => 0,
                'password_min_length' => 8,
                'password_require_uppercase' => 1,
                'password_require_lowercase' => 1,
                'password_require_numbers' => 1,
                'password_require_symbols' => 0,
                'login_attempts_limit' => 5,
                'login_lockout_duration' => 900,
                'admin_activity_log' => 1,
                'failed_login_log' => 1
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за IP ограничения
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareIPRestrictionData() {
        // Получаване на IP whitelist от настройките
        $ip_whitelist = $this->getData('ip_whitelist') ?: '';
        $allowed_ips = [];

        if (!empty($ip_whitelist)) {
            $allowed_ips = array_filter(array_map('trim', explode("\n", $ip_whitelist)));
        }

        // Задаване на данните
        $this->setData('allowed_ips', $allowed_ips);
        $this->setData('allowed_ips_text', $ip_whitelist);

        // Задаване на ip_restriction_enabled базирано на ip_blocking_enabled
        $ip_blocking_enabled = $this->getData('ip_blocking_enabled') ?: 0;
        $this->setData('ip_restriction_enabled', $ip_blocking_enabled);

        // Получаване на текущия IP адрес
        $current_ip = $this->request->server['REMOTE_ADDR'] ?? 'unknown';
        $this->setData('current_ip', $current_ip);

        // Проверка дали текущият IP е в списъка
        $is_current_ip_allowed = empty($allowed_ips) || in_array($current_ip, $allowed_ips);
        $this->setData('is_current_ip_allowed', $is_current_ip_allowed);

        return $this;
    }

    /**
     * Подготвя данните за сесии
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareSessionData() {
        // Опции за timeout на сесии
        $session_timeout_options = [
            900 => '15 минути',
            1800 => '30 минути',
            3600 => '1 час',
            7200 => '2 часа',
            14400 => '4 часа',
            28800 => '8 часа',
            86400 => '24 часа'
        ];

        $this->setData('session_timeout_options', $session_timeout_options);

        // Информация за текущата сесия
        $session_info = [
            'session_id' => session_id(),
            'session_start' => $_SESSION['session_start'] ?? time(),
            'last_activity' => $_SESSION['last_activity'] ?? time(),
            'user_agent' => $this->request->server['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        $this->setData('session_info', $session_info);

        return $this;
    }

    /**
     * Подготвя данните за политика за пароли
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePasswordPolicyData() {
        // Опции за минимална дължина на паролата
        $password_length_options = [];
        for ($i = 6; $i <= 20; $i++) {
            $password_length_options[$i] = $i . ' символа';
        }

        $this->setData('password_length_options', $password_length_options);

        // Опции за лимит на опити за вход
        $login_attempts_options = [
            3 => '3 опита',
            5 => '5 опита',
            10 => '10 опита',
            15 => '15 опита',
            0 => 'Без ограничение'
        ];

        $this->setData('login_attempts_options', $login_attempts_options);

        // Опции за продължителност на блокиране
        $lockout_duration_options = [
            300 => '5 минути',
            600 => '10 минути',
            900 => '15 минути',
            1800 => '30 минути',
            3600 => '1 час',
            7200 => '2 часа',
            86400 => '24 часа'
        ];

        $this->setData('lockout_duration_options', $lockout_duration_options);

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/security_save'),
            'test_ip_url' => $this->getAdminLink('setting/setting/test_ip_restriction'),
            'clear_failed_logins_url' => $this->getAdminLink('setting/setting/clear_failed_logins'),
            'export_activity_log_url' => $this->getAdminLink('setting/setting/export_activity_log')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Подготвя правила за валидация
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareValidationRules() {
        $validation_rules = [
            'allowed_ips_text' => [
                'required' => false,
                'type' => 'ip_list'
            ],
            'session_timeout' => [
                'required' => true,
                'type' => 'number',
                'min' => 300,
                'max' => 86400
            ],
            'password_min_length' => [
                'required' => true,
                'type' => 'number',
                'min' => 6,
                'max' => 20
            ],
            'login_attempts_limit' => [
                'required' => true,
                'type' => 'number',
                'min' => 0,
                'max' => 50
            ],
            'login_lockout_duration' => [
                'required' => true,
                'type' => 'number',
                'min' => 60,
                'max' => 86400
            ]
        ];

        $this->setData('validation_rules', $validation_rules);

        return $this;
    }

    /**
     * Получава статистики за настройките за сигурност
     *
     * @return array
     */
    private function getSecurityStatistics() {
        try {
            $this->loadModelAs('setting/security', 'securityModel');

            return $this->securityModel->getSecurityStatistics();
            
        } catch (Exception $e) {
            return [
                'failed_login_attempts' => 0,
                'active_sessions' => 1,
                'blocked_ips' => 0,
                'security_score' => 50
            ];
        }
    }

    /**
     * Изчислява оценка за сигурността
     *
     * @return int Оценка от 0 до 100
     */
    private function calculateSecurityScore() {
        $score = 0;
        $settings = $this->getData();
        
        // IP ограничения (+20 точки)
        if (!empty($settings['ip_restriction_enabled'])) {
            $score += 20;
        }
        
        // SSL принуждаване (+15 точки)
        if (!empty($settings['force_ssl'])) {
            $score += 15;
        }
        

        
        // Силна политика за пароли (+20 точки)
        if ($settings['password_min_length'] >= 8 && 
            $settings['password_require_uppercase'] && 
            $settings['password_require_lowercase'] && 
            $settings['password_require_numbers']) {
            $score += 20;
        }
        
        // Ограничения за вход (+10 точки)
        if ($settings['login_attempts_limit'] > 0 && $settings['login_attempts_limit'] <= 5) {
            $score += 10;
        }
        
        // Логиране на активност (+10 точки)
        if (!empty($settings['admin_activity_log']) && !empty($settings['failed_login_log'])) {
            $score += 10;
        }
        
        return min(100, $score);
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'currentIP' => $this->getData('current_ip'),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testIP' => $this->getData('test_ip_url'),
                'clearFailedLogins' => $this->getData('clear_failed_logins_url'),
                'exportActivityLog' => $this->getData('export_activity_log_url')
            ],
            'validationRules' => $this->getData('validation_rules'),
            'securityScore' => $this->calculateSecurityScore()
        ];
    }
}
