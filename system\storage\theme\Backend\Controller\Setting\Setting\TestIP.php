<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за тестване на IP ограничения
 */
class TestIP extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX тестване на IP ограничения
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за тестване на IP ограничения';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на текущия IP адрес
            $current_ip = $this->request->server['REMOTE_ADDR'] ?? 'unknown';
            
            // Зареждане на модела за настройки за сигурност
            $this->loadModelAs('setting/security', 'securitySettings');
            $settings = $this->securitySettings->getSettings();
            
            // Проверка дали IP ограниченията са активирани
            if (empty($settings['ip_blocking_enabled'])) {
                $json['success'] = true;
                $json['message'] = 'IP ограниченията не са активирани. Всички IP адреси имат достъп.';
                $this->outputJson($json);
                return;
            }
            
            // Получаване на списъка с разрешени IP адреси
            $ip_whitelist = $settings['ip_whitelist'] ?? '';
            $allowed_ips = [];
            
            if (!empty($ip_whitelist)) {
                $allowed_ips = array_filter(array_map('trim', explode("\n", $ip_whitelist)));
            }
            
            if (empty($allowed_ips)) {
                $json['warning'] = true;
                $json['message'] = 'IP ограниченията са активирани, но няма зададени разрешени IP адреси. Това ще блокира всички потребители!';
                $this->outputJson($json);
                return;
            }
            
            // Проверка дали текущият IP е в списъка
            $is_allowed = $this->isIPAllowed($current_ip, $allowed_ips);
            
            if ($is_allowed) {
                $json['success'] = true;
                $json['message'] = "Вашият IP адрес ({$current_ip}) е в списъка с разрешени адреси. Достъпът ще бъде разрешен.";
            } else {
                $json['warning'] = true;
                $json['message'] = "ВНИМАНИЕ: Вашият IP адрес ({$current_ip}) НЕ Е в списъка с разрешени адреси. Ще загубите достъп до административния панел!";
            }
            
            // Добавяне на допълнителна информация
            $json['current_ip'] = $current_ip;
            $json['allowed_ips_count'] = count($allowed_ips);
            $json['test_timestamp'] = date('d.m.Y H:i:s');

        } catch (Exception $e) {
            $json['error'] = 'Грешка при тестването: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Проверява дали IP адресът е разрешен
     *
     * @param string $ip IP адрес за проверка
     * @param array $allowed_ips Списък с разрешени IP адреси
     * @return bool
     */
    private function isIPAllowed($ip, $allowed_ips) {
        foreach ($allowed_ips as $allowed_ip) {
            // Проверка за точно съвпадение
            if ($ip === $allowed_ip) {
                return true;
            }
            
            // Проверка за CIDR нотация
            if (strpos($allowed_ip, '/') !== false) {
                if ($this->isIPInCIDR($ip, $allowed_ip)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Проверява дали IP адресът е в CIDR диапазон
     *
     * @param string $ip IP адрес за проверка
     * @param string $cidr CIDR нотация (напр. ***********/24)
     * @return bool
     */
    private function isIPInCIDR($ip, $cidr) {
        list($subnet, $mask) = explode('/', $cidr);
        
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4) || 
            !filter_var($subnet, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }
        
        $mask = (int)$mask;
        if ($mask < 0 || $mask > 32) {
            return false;
        }
        
        $ip_long = ip2long($ip);
        $subnet_long = ip2long($subnet);
        $mask_long = -1 << (32 - $mask);
        
        return ($ip_long & $mask_long) === ($subnet_long & $mask_long);
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
}
