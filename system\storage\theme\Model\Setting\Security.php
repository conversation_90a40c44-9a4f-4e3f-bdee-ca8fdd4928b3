<?php

namespace Theme25\Model\Setting;

use Theme25\Helper\SettingValidator;

/**
 * Модел за настройки на сигурност
 */
class Security extends \Theme25\Model\Setting {

    private $validator;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->settingPrefix = 'security';
        $this->validator = new SettingValidator($registry);
    }

    /**
     * Получава всички настройки за сигурност
     * 
     * @return array
     */
    public function getSettings() {

        $settings = $this->getMultipleSettings([
            // SSL и HTTPS
            'ssl_enabled' => 1,
            'force_ssl' => 1,
            'ssl_redirect' => 1,
            'hsts_enabled' => 1,
            'hsts_max_age' => 31536000,
            
            // Firewall и IP защита
            'firewall_enabled' => 1,
            'ip_blocking_enabled' => 1,
            'ip_whitelist' => '',
            'ip_blacklist' => '',
            'rate_limiting_enabled' => 1,
            'rate_limit_requests' => 100,
            'rate_limit_window' => 3600,
            'ddos_protection_enabled' => 1,
            
            // Защита от атаки
            'csrf_protection_enabled' => 1,
            'xss_protection_enabled' => 1,
            'sql_injection_protection_enabled' => 1,
            'file_upload_protection_enabled' => 1,
            'directory_traversal_protection_enabled' => 1,
            'clickjacking_protection_enabled' => 1,
            
            // Парола политики
            'password_complexity_enabled' => 1,
            'password_min_length' => 8,
            'password_require_uppercase' => 1,
            'password_require_lowercase' => 1,
            'password_require_numbers' => 1,
            'password_require_symbols' => 0,
            'password_history_enabled' => 1,
            'password_history_count' => 5,
            'password_expiry_enabled' => 0,
            'password_expiry_days' => 90,
            
            // Сесии
            'session_security_enabled' => 1,
            'session_timeout' => 3600,
            'session_regenerate_enabled' => 1,
            'session_regenerate_interval' => 300,
            'concurrent_sessions_limit' => 1,
            'session_ip_validation' => 1,
            'session_user_agent_validation' => 1,
            
            // Логиране и мониторинг
            'security_logging_enabled' => 1,
            'failed_login_logging' => 1,
            'admin_action_logging' => 1,
            'file_change_monitoring' => 1,
            'suspicious_activity_detection' => 1,
            'log_retention_days' => 90,
            
            // Backup и възстановяване
            'auto_backup_enabled' => 1,
            'backup_frequency' => 'daily',
            'backup_retention_days' => 30,
            'backup_encryption_enabled' => 1,
            'backup_remote_storage' => 0,
            
            // Известия за сигурност
            'security_notifications_enabled' => 1,
            'failed_login_notifications' => 1,
            'suspicious_activity_notifications' => 1,
            'file_change_notifications' => 1,
            'backup_notifications' => 1,
            'security_admin_email' => '',
            

            
            // Captcha
            'captcha_enabled' => 1,
            'captcha_provider' => 'recaptcha',
            'captcha_site_key' => '',
            'captcha_secret_key' => '',
            'captcha_login_enabled' => 1,
            'captcha_register_enabled' => 1,
            'captcha_contact_enabled' => 1,
            
            // Файлова сигурност
            'file_permissions_check' => 1,
            'malware_scanning_enabled' => 0,
            'file_integrity_check' => 1,
            'upload_file_scanning' => 1,
            'allowed_file_types' => 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx',
            'max_upload_size' => 10485760, // 10MB
            
            // Headers за сигурност
            'security_headers_enabled' => 1,
            'content_security_policy' => "default-src 'self'",
            'x_frame_options' => 'SAMEORIGIN',
            'x_content_type_options' => 'nosniff',
            'referrer_policy' => 'strict-origin-when-cross-origin'
        ], true); // използва основната база данни

        return $settings;
    }

    /**
     * Запазва настройките за сигурност
     * 
     * @param array $data Данни за запазване
     * @return bool|array True при успех, масив с грешки при неуспех
     */
    public function saveSettings($data) {

        // Валидация на данните
        $errors = $this->validateSettings($data);
        if (!empty($errors)) {
            return $errors;
        }

        // Подготовка на данните за запазване
        $settingsToSave = [
            // SSL и HTTPS
            'ssl_enabled' => isset($data['ssl_enabled']) ? (int)$data['ssl_enabled'] : 1,
            'force_ssl' => isset($data['force_ssl']) ? (int)$data['force_ssl'] : 1,
            'ssl_redirect' => isset($data['ssl_redirect']) ? (int)$data['ssl_redirect'] : 1,
            'hsts_enabled' => isset($data['hsts_enabled']) ? (int)$data['hsts_enabled'] : 1,
            'hsts_max_age' => (int)($data['hsts_max_age'] ?? 31536000),
            
            // Firewall и IP защита
            'firewall_enabled' => isset($data['firewall_enabled']) ? (int)$data['firewall_enabled'] : 1,
            'ip_blocking_enabled' => isset($data['ip_blocking_enabled']) ? (int)$data['ip_blocking_enabled'] : 1,
            'ip_whitelist' => $data['ip_whitelist'] ?? '',
            'ip_blacklist' => $data['ip_blacklist'] ?? '',
            'rate_limiting_enabled' => isset($data['rate_limiting_enabled']) ? (int)$data['rate_limiting_enabled'] : 1,
            'rate_limit_requests' => (int)($data['rate_limit_requests'] ?? 100),
            'rate_limit_window' => (int)($data['rate_limit_window'] ?? 3600),
            'ddos_protection_enabled' => isset($data['ddos_protection_enabled']) ? (int)$data['ddos_protection_enabled'] : 1,
            
            // Защита от атаки
            'csrf_protection_enabled' => isset($data['csrf_protection_enabled']) ? (int)$data['csrf_protection_enabled'] : 1,
            'xss_protection_enabled' => isset($data['xss_protection_enabled']) ? (int)$data['xss_protection_enabled'] : 1,
            'sql_injection_protection_enabled' => isset($data['sql_injection_protection_enabled']) ? (int)$data['sql_injection_protection_enabled'] : 1,
            'file_upload_protection_enabled' => isset($data['file_upload_protection_enabled']) ? (int)$data['file_upload_protection_enabled'] : 1,
            'directory_traversal_protection_enabled' => isset($data['directory_traversal_protection_enabled']) ? (int)$data['directory_traversal_protection_enabled'] : 1,
            'clickjacking_protection_enabled' => isset($data['clickjacking_protection_enabled']) ? (int)$data['clickjacking_protection_enabled'] : 1,
            
            // Парола политики
            'password_complexity_enabled' => isset($data['password_complexity_enabled']) ? (int)$data['password_complexity_enabled'] : 1,
            'password_min_length' => (int)($data['password_min_length'] ?? 8),
            'password_require_uppercase' => isset($data['password_require_uppercase']) ? (int)$data['password_require_uppercase'] : 1,
            'password_require_lowercase' => isset($data['password_require_lowercase']) ? (int)$data['password_require_lowercase'] : 1,
            'password_require_numbers' => isset($data['password_require_numbers']) ? (int)$data['password_require_numbers'] : 1,
            'password_require_symbols' => isset($data['password_require_symbols']) ? (int)$data['password_require_symbols'] : 0,
            'password_history_enabled' => isset($data['password_history_enabled']) ? (int)$data['password_history_enabled'] : 1,
            'password_history_count' => (int)($data['password_history_count'] ?? 5),
            'password_expiry_enabled' => isset($data['password_expiry_enabled']) ? (int)$data['password_expiry_enabled'] : 0,
            'password_expiry_days' => (int)($data['password_expiry_days'] ?? 90),
            
            // Сесии
            'session_security_enabled' => isset($data['session_security_enabled']) ? (int)$data['session_security_enabled'] : 1,
            'session_timeout' => (int)($data['session_timeout'] ?? 3600),
            'session_regenerate_enabled' => isset($data['session_regenerate_enabled']) ? (int)$data['session_regenerate_enabled'] : 1,
            'session_regenerate_interval' => (int)($data['session_regenerate_interval'] ?? 300),
            'concurrent_sessions_limit' => (int)($data['concurrent_sessions_limit'] ?? 1),
            'session_ip_validation' => isset($data['session_ip_validation']) ? (int)$data['session_ip_validation'] : 1,
            'session_user_agent_validation' => isset($data['session_user_agent_validation']) ? (int)$data['session_user_agent_validation'] : 1,
            
            // Логиране и мониторинг
            'security_logging_enabled' => isset($data['security_logging_enabled']) ? (int)$data['security_logging_enabled'] : 1,
            'failed_login_logging' => isset($data['failed_login_logging']) ? (int)$data['failed_login_logging'] : 1,
            'admin_action_logging' => isset($data['admin_action_logging']) ? (int)$data['admin_action_logging'] : 1,
            'file_change_monitoring' => isset($data['file_change_monitoring']) ? (int)$data['file_change_monitoring'] : 1,
            'suspicious_activity_detection' => isset($data['suspicious_activity_detection']) ? (int)$data['suspicious_activity_detection'] : 1,
            'log_retention_days' => (int)($data['log_retention_days'] ?? 90),
            
            // Backup и възстановяване
            'auto_backup_enabled' => isset($data['auto_backup_enabled']) ? (int)$data['auto_backup_enabled'] : 1,
            'backup_frequency' => $data['backup_frequency'] ?? 'daily',
            'backup_retention_days' => (int)($data['backup_retention_days'] ?? 30),
            'backup_encryption_enabled' => isset($data['backup_encryption_enabled']) ? (int)$data['backup_encryption_enabled'] : 1,
            'backup_remote_storage' => isset($data['backup_remote_storage']) ? (int)$data['backup_remote_storage'] : 0,
            
            // Известия за сигурност
            'security_notifications_enabled' => isset($data['security_notifications_enabled']) ? (int)$data['security_notifications_enabled'] : 1,
            'failed_login_notifications' => isset($data['failed_login_notifications']) ? (int)$data['failed_login_notifications'] : 1,
            'suspicious_activity_notifications' => isset($data['suspicious_activity_notifications']) ? (int)$data['suspicious_activity_notifications'] : 1,
            'file_change_notifications' => isset($data['file_change_notifications']) ? (int)$data['file_change_notifications'] : 1,
            'backup_notifications' => isset($data['backup_notifications']) ? (int)$data['backup_notifications'] : 1,
            'security_admin_email' => $data['security_admin_email'] ?? '',
            

            
            // Captcha
            'captcha_enabled' => isset($data['captcha_enabled']) ? (int)$data['captcha_enabled'] : 1,
            'captcha_provider' => $data['captcha_provider'] ?? 'recaptcha',
            'captcha_site_key' => $data['captcha_site_key'] ?? '',
            'captcha_secret_key' => $data['captcha_secret_key'] ?? '',
            'captcha_login_enabled' => isset($data['captcha_login_enabled']) ? (int)$data['captcha_login_enabled'] : 1,
            'captcha_register_enabled' => isset($data['captcha_register_enabled']) ? (int)$data['captcha_register_enabled'] : 1,
            'captcha_contact_enabled' => isset($data['captcha_contact_enabled']) ? (int)$data['captcha_contact_enabled'] : 1,
            
            // Файлова сигурност
            'file_permissions_check' => isset($data['file_permissions_check']) ? (int)$data['file_permissions_check'] : 1,
            'malware_scanning_enabled' => isset($data['malware_scanning_enabled']) ? (int)$data['malware_scanning_enabled'] : 0,
            'file_integrity_check' => isset($data['file_integrity_check']) ? (int)$data['file_integrity_check'] : 1,
            'upload_file_scanning' => isset($data['upload_file_scanning']) ? (int)$data['upload_file_scanning'] : 1,
            'allowed_file_types' => $data['allowed_file_types'] ?? 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx',
            'max_upload_size' => (int)($data['max_upload_size'] ?? 10485760),
            
            // Headers за сигурност
            'security_headers_enabled' => isset($data['security_headers_enabled']) ? (int)$data['security_headers_enabled'] : 1,
            'content_security_policy' => $data['content_security_policy'] ?? "default-src 'self'",
            'x_frame_options' => $data['x_frame_options'] ?? 'SAMEORIGIN',
            'x_content_type_options' => $data['x_content_type_options'] ?? 'nosniff',
            'referrer_policy' => $data['referrer_policy'] ?? 'strict-origin-when-cross-origin'
        ];

        // Запазване на настройките
        return $this->setMultipleSettings($settingsToSave, true);
    }

    /**
     * Валидира настройките за сигурност
     * 
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    protected function validateSettings($data) {
        $errors = [];

        // Валидация на HSTS max age
        if (isset($data['hsts_max_age'])) {
            $maxAge = (int)$data['hsts_max_age'];
            if ($maxAge < 300 || $maxAge > 63072000) { // 5 минути до 2 години
                $errors['hsts_max_age'] = 'HSTS max age трябва да бъде между 300 и 63072000 секунди';
            }
        }

        // Валидация на rate limit
        if (isset($data['rate_limit_requests'])) {
            $requests = (int)$data['rate_limit_requests'];
            if ($requests < 1 || $requests > 10000) {
                $errors['rate_limit_requests'] = 'Rate limit заявки трябва да бъдат между 1 и 10000';
            }
        }

        if (isset($data['rate_limit_window'])) {
            $window = (int)$data['rate_limit_window'];
            if ($window < 60 || $window > 86400) {
                $errors['rate_limit_window'] = 'Rate limit прозорец трябва да бъде между 60 и 86400 секунди';
            }
        }

        // Валидация на минимална дължина на паролата
        if (isset($data['password_min_length'])) {
            $minLength = (int)$data['password_min_length'];
            if ($minLength < 4 || $minLength > 128) {
                $errors['password_min_length'] = 'Минималната дължина на паролата трябва да бъде между 4 и 128 символа';
            }
        }

        // Валидация на история на пароли
        if (isset($data['password_history_count'])) {
            $historyCount = (int)$data['password_history_count'];
            if ($historyCount < 1 || $historyCount > 50) {
                $errors['password_history_count'] = 'Броят на паролите в историята трябва да бъде между 1 и 50';
            }
        }

        // Валидация на дни за изтичане на паролата
        if (isset($data['password_expiry_days'])) {
            $expiryDays = (int)$data['password_expiry_days'];
            if ($expiryDays < 1 || $expiryDays > 365) {
                $errors['password_expiry_days'] = 'Дните за изтичане на паролата трябва да бъдат между 1 и 365';
            }
        }

        // Валидация на session timeout
        if (isset($data['session_timeout'])) {
            $timeout = (int)$data['session_timeout'];
            if ($timeout < 300 || $timeout > 86400) {
                $errors['session_timeout'] = 'Session timeout трябва да бъде между 300 и 86400 секунди';
            }
        }

        // Валидация на session regenerate interval
        if (isset($data['session_regenerate_interval'])) {
            $interval = (int)$data['session_regenerate_interval'];
            if ($interval < 60 || $interval > 3600) {
                $errors['session_regenerate_interval'] = 'Session regenerate interval трябва да бъде между 60 и 3600 секунди';
            }
        }

        // Валидация на log retention days
        if (isset($data['log_retention_days'])) {
            $retentionDays = (int)$data['log_retention_days'];
            if ($retentionDays < 1 || $retentionDays > 365) {
                $errors['log_retention_days'] = 'Дните за съхранение на логове трябва да бъдат между 1 и 365';
            }
        }

        // Валидация на backup retention days
        if (isset($data['backup_retention_days'])) {
            $backupDays = (int)$data['backup_retention_days'];
            if ($backupDays < 1 || $backupDays > 365) {
                $errors['backup_retention_days'] = 'Дните за съхранение на backup трябва да бъдат между 1 и 365';
            }
        }

        // Валидация на email адрес за сигурност
        if (!empty($data['security_admin_email'])) {
            $emailValidation = $this->validator->validateEmail($data['security_admin_email']);
            if ($emailValidation !== true) {
                $errors['security_admin_email'] = $emailValidation;
            }
        }

        // Валидация на максимален размер за upload
        if (isset($data['max_upload_size'])) {
            $maxSize = (int)$data['max_upload_size'];
            if ($maxSize < 1024 || $maxSize > 104857600) { // 1KB до 100MB
                $errors['max_upload_size'] = 'Максималният размер за upload трябва да бъде между 1KB и 100MB';
            }
        }

        // Валидация на IP адреси в whitelist и blacklist
        if (!empty($data['ip_whitelist'])) {
            $ipList = explode(',', $data['ip_whitelist']);
            foreach ($ipList as $ip) {
                $ip = trim($ip);
                if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
                    $errors['ip_whitelist'] = 'Невалиден IP адрес в whitelist: ' . $ip;
                    break;
                }
            }
        }

        if (!empty($data['ip_blacklist'])) {
            $ipList = explode(',', $data['ip_blacklist']);
            foreach ($ipList as $ip) {
                $ip = trim($ip);
                if (!empty($ip) && !filter_var($ip, FILTER_VALIDATE_IP)) {
                    $errors['ip_blacklist'] = 'Невалиден IP адрес в blacklist: ' . $ip;
                    break;
                }
            }
        }

        return $errors;
    }

    /**
     * Получава налични методи за двуфакторна автентикация
     * 
     * @return array
     */
    public function getTwoFactorMethods() {
        return [
            'email' => 'Email',
            'sms' => 'SMS',
            'app' => 'Authenticator App',
            'backup_codes' => 'Backup Codes'
        ];
    }

    /**
     * Получава налични captcha доставчици
     * 
     * @return array
     */
    public function getCaptchaProviders() {
        return [
            'recaptcha' => 'Google reCAPTCHA',
            'hcaptcha' => 'hCaptcha',
            'turnstile' => 'Cloudflare Turnstile'
        ];
    }

    /**
     * Получава статистики за сигурността
     * 
     * @return array
     */
    public function getSecurityStatistics() {
        try {
            return [
                'failed_logins_today' => 0,
                'blocked_ips_count' => 0,
                'security_events_today' => 0,
                'last_backup_date' => null,
                'ssl_certificate_expiry' => null,
                'security_score' => 85
            ];
        } catch (Exception $e) {
            return [
                'failed_logins_today' => 0,
                'blocked_ips_count' => 0,
                'security_events_today' => 0,
                'last_backup_date' => null,
                'ssl_certificate_expiry' => null,
                'security_score' => 85
            ];
        }
    }
}
