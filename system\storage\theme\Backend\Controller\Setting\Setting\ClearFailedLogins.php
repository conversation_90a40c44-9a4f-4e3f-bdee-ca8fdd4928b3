<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за изчистване на неуспешни опити за вход
 */
class ClearFailedLogins extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX изчистване на неуспешни опити за вход
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за изчистване на неуспешни опити за вход';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Изчистване на неуспешни опити за вход от различни таблици
            $cleared_count = 0;
            
            // Изчистване от таблицата за неуспешни опити за вход на администратори
            $result1 = $this->clearAdminFailedLogins();
            $cleared_count += $result1;
            
            // Изчистване от таблицата за неуспешни опити за вход на клиенти
            $result2 = $this->clearCustomerFailedLogins();
            $cleared_count += $result2;
            
            // Изчистване от общата таблица за логове
            $result3 = $this->clearSecurityLogs();
            $cleared_count += $result3;
            
            // Логиране на действието
            $this->logClearFailedLoginsAction($cleared_count);
            
            $json['success'] = true;
            $json['message'] = "Успешно изчистени {$cleared_count} записа за неуспешни опити за вход";
            $json['cleared_count'] = $cleared_count;
            $json['timestamp'] = date('d.m.Y H:i:s');

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изчистването: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Изчиства неуспешни опити за вход на администратори
     *
     * @return int Брой изчистени записи
     */
    private function clearAdminFailedLogins() {
        try {
            // Проверка дали таблицата съществува
            $table_check = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . "user_login_attempt'");
            
            if ($table_check->num_rows == 0) {
                return 0;
            }
            
            // Изчистване на записите
            $this->dbQuery("DELETE FROM `" . DB_PREFIX . "user_login_attempt` WHERE 1");
            
            return $this->db->countAffected();
            
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Изчиства неуспешни опити за вход на клиенти
     *
     * @return int Брой изчистени записи
     */
    private function clearCustomerFailedLogins() {
        try {
            // Проверка дали таблицата съществува
            $table_check = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . "customer_login_attempt'");
            
            if ($table_check->num_rows == 0) {
                return 0;
            }
            
            // Изчистване на записите
            $this->dbQuery("DELETE FROM `" . DB_PREFIX . "customer_login_attempt` WHERE 1");
            
            return $this->db->countAffected();
            
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Изчиства логове за сигурност
     *
     * @return int Брой изчистени записи
     */
    private function clearSecurityLogs() {
        try {
            // Проверка дали таблицата съществува
            $table_check = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . "security_log'");
            
            if ($table_check->num_rows == 0) {
                return 0;
            }
            
            // Изчистване само на записите за неуспешни опити за вход
            $this->dbQuery("DELETE FROM `" . DB_PREFIX . "security_log` 
                           WHERE action IN ('failed_login', 'blocked_login', 'suspicious_login')");
            
            return $this->db->countAffected();
            
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Логира действието за изчистване
     *
     * @param int $cleared_count Брой изчистени записи
     */
    private function logClearFailedLoginsAction($cleared_count) {
        try {
            // Логиране в системния лог
            $user_info = [
                'id' => $this->user->getId(),
                'username' => $this->user->getUserName(),
                'ip' => $this->request->server['REMOTE_ADDR'] ?? 'unknown'
            ];
            
            $log_message = sprintf(
                'Failed login attempts cleared by admin %s (ID: %d, IP: %s). Cleared %d records.',
                $user_info['username'],
                $user_info['id'],
                $user_info['ip'],
                $cleared_count
            );
            
            $this->log->write($log_message);
            
            // Ако има таблица за логове за сигурност, записваме и там
            $this->logToSecurityTable($user_info, $cleared_count);
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log clear failed logins action: ' . $e->getMessage());
        }
    }

    /**
     * Записва в таблицата за логове за сигурност
     *
     * @param array $user_info Информация за потребителя
     * @param int $cleared_count Брой изчистени записи
     */
    private function logToSecurityTable($user_info, $cleared_count) {
        try {
            // Проверка дали таблицата съществува
            $table_check = $this->dbQuery("SHOW TABLES LIKE '" . DB_PREFIX . "security_log'");
            
            if ($table_check->num_rows > 0) {
                $this->dbQuery("INSERT INTO `" . DB_PREFIX . "security_log` SET
                    user_id = '" . (int)$user_info['id'] . "',
                    username = '" . $this->dbEscape($user_info['username']) . "',
                    action = 'clear_failed_logins',
                    details = '" . $this->dbEscape(json_encode(['cleared_count' => $cleared_count])) . "',
                    ip_address = '" . $this->dbEscape($user_info['ip']) . "',
                    user_agent = '" . $this->dbEscape($this->request->server['HTTP_USER_AGENT'] ?? '') . "',
                    date_added = NOW()");
            }
            
        } catch (Exception $e) {
            // Тихо игнорираме грешки при логирането
        }
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
}
