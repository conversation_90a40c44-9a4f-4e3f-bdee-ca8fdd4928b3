---
type: "agent_requested"
description: "Спесифични правила за работа с темата на Rakla.bg"
---
- Шаблоните на темата, която разработвам не са стандартни Opencart шаблони. Те са в twig формат, с Tailwind оформление, без header, footer, left_column, right_column..
- Новите javascript модули трябва да разширяват основния backend.js (BackendModule).
- Много внимателно разгледай главния контролер Theme25\Controller какви методи съдържа и за какво се използват и ги прилагай във всички контролери и суб-контролери на темата, където може, за да се спестява код и кодът да бъде по-четим и лесен за поддръжка.
- За паджинация, винаги използвай класът Theme25\Pagination вместо стандартния за Opencart. Виж как е реализиран в контролера F:\Web\Rakla.bg - NEW\system\storage\theme\Backend\Controller\Catalog\Product\Index.php и как се използва метода preparePagination().
- Методите в контролери, които извеждат съдържание от шабон в Backend частта, винаги трябва да започват с обръщение към метода $this->initAdminData();
- При работа с php контролери, използвай за референция следния файл: F:\Web\Rakla.bg - NEW\Controller_Methods_Documentation.md, за да оптимизираш кодът