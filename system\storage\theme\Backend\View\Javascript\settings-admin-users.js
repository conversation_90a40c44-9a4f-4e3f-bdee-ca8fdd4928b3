/**
 * JavaScript модул за настройки на админ потребители
 * Следва BackendModule pattern на темата Rakla.bg
 * Разширява основния settings модул
 */
(function() {
    'use strict';

    // Функция за инициализация на admin users модула
    function initAdminUsersModule() {
        if (typeof BackendModule !== 'undefined' && typeof BackendModule === 'object') {
            // Добавяне на функционалност към основния модул
            extendBackendModuleWithAdminUsers();
        } else {
            console.error('BackendModule not found');
        }
    }

    // Разширяване на основния модул с функционалности за админ потребители
    function extendBackendModuleWithAdminUsers() {
        Object.assign(BackendModule, {
            /**
             * Инициализиране на настройки за админ потребители
             */
            initAdminUsersSettings: function() {
                this.bindAdminUsersEvents();
                this.initUserTable();
                this.initUserGroupsTable();
                this.logDev && this.logDev('Admin users settings module initialized');
            },

            /**
             * Свързване на събития за настройки за админ потребители
             */
            bindAdminUsersEvents: function() {
                const self = this;

                // Забележка: Събитията сега се обработват чрез event delegation
                // в handleTabClick, handleTabFormSubmit и handleTabChange функциите
                // Това осигурява правилно функциониране дори когато съдържанието
                // на таба се зарежда динамично чрез AJAX

                self.logDev && self.logDev('Admin users events will be handled via event delegation');
            },

            /**
             * Обработка на click събития за admin users таб
             */
            handleAdminUsersClick: function(e) {
                const target = e.target;
                const self = this;

                // Проверяваме за специфични класове и ID-та
                const addUserBtn = target.closest('#add-admin-user, .add-admin-user');
                const addGroupBtn = target.closest('#add-user-group, .add-user-group');
                const editUserBtn = target.closest('.edit-user');
                const deleteUserBtn = target.closest('.delete-user');
                const toggleStatusBtn = target.closest('.toggle-user-status');
                const resetPassBtn = target.closest('.reset-user-password');
                const unlockUserBtn = target.closest('.unlock-user');
                const editGroupBtn = target.closest('.edit-user-group');
                const deleteGroupBtn = target.closest('.delete-user-group');
                const managePermsBtn = target.closest('.manage-permissions');
                const saveSettingsBtn = target.closest('#save-admin-users-settings');

                if (addUserBtn) {
                    e.preventDefault();
                    self.showAddUserModal();
                } else if (addGroupBtn) {
                    e.preventDefault();
                    self.showAddUserGroupModal();
                } else if (editUserBtn) {
                    e.preventDefault();
                    const userId = editUserBtn.getAttribute('data-user-id');
                    self.editUser(userId);
                } else if (deleteUserBtn) {
                    e.preventDefault();
                    const userId = deleteUserBtn.getAttribute('data-user-id');
                    self.deleteUser(userId);
                } else if (toggleStatusBtn) {
                    e.preventDefault();
                    const userId = toggleStatusBtn.getAttribute('data-user-id');
                    self.toggleUserStatus(userId);
                } else if (resetPassBtn) {
                    e.preventDefault();
                    const userId = resetPassBtn.getAttribute('data-user-id');
                    self.resetUserPassword(userId);
                } else if (unlockUserBtn) {
                    e.preventDefault();
                    const userId = unlockUserBtn.getAttribute('data-user-id');
                    self.unlockUser(userId);
                } else if (editGroupBtn) {
                    e.preventDefault();
                    const groupId = editGroupBtn.getAttribute('data-group-id');
                    self.editUserGroup(groupId);
                } else if (deleteGroupBtn) {
                    e.preventDefault();
                    const groupId = deleteGroupBtn.getAttribute('data-group-id');
                    self.deleteUserGroup(groupId);
                } else if (managePermsBtn) {
                    e.preventDefault();
                    const groupId = managePermsBtn.getAttribute('data-group-id');
                    self.managePermissions(groupId);
                } else if (saveSettingsBtn) {
                    e.preventDefault();
                    self.saveAdminUsersSettings();
                }
                else {
                    self.handleAdminUsersFormSubmit(e);
                    self.handleAdminUsersChange(e);
                }
            },

            /**
             * Обработка на form submit събития за admin users таб
             */
            handleAdminUsersFormSubmit: function(e) {
                e.preventDefault();
                const form = e.target;
                const self = this;

                self.logDev('handleAdminUsersFormSubmit called' + form.id);

                if (form.id === 'add-user-form') {
                    self.submitAddUserForm(form);
                } else if (form.id === 'add-group-form') {
                    self.submitAddGroupForm(form);
                } else if (form.id === 'edit-user-form') {
                    self.submitEditUserForm(form);
                } else if (form.id === 'edit-group-form') {
                    // TODO: Implement edit group form submission
                    self.logDev && self.logDev('Edit group form submission not yet implemented');
                } else if (form.id === 'admin-users-settings-form') {
                    self.saveAdminUsersSettings();
                }
            },

            /**
             * Обработка на change събития за admin users таб
             */
            handleAdminUsersChange: function(e) {
                const target = e.target;
                const self = this;

                // Автоматично запазване при промяна на checkbox-и
                if (target.type === 'checkbox' && target.name && target.name.includes('setting')) {
                    self.autoSaveSettings();
                }

                // IP whitelist toggle
                if (target.id === 'ip_whitelist_enabled') {
                    self.toggleIPWhitelistFields(target.checked);
                }
            },

            /**
             * Запазване на настройки за админ потребители
             */
            saveAdminUsersSettings: function() {
                const self = this;
                const form = document.getElementById('admin-users-settings-form');

                if (!form) {
                    self.showSettingsNotification('Формата не е намерена', 'error');
                    return;
                }

                // Валидация преди запазване
                const validationErrors = self.validateAdminUsersForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Показване на loading състояние
                const saveButton = document.getElementById('save-admin-users-settings');
                if (saveButton) {
                    saveButton.disabled = true;
                    saveButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                fetch(self.settings.config.ajaxUrls.admin_users_save || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Актуализиране на данните ако са предоставени
                        if (data.updated_data) {
                            self.updateAdminUsersSettingsDisplay(data.updated_data);
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error saving admin users settings:', error);
                    self.showSettingsNotification('Възникна грешка при запазването', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (saveButton) {
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Обработка на формата за добавяне на потребител
             */
            submitAddUserForm: function(form) {
                const self = this;
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Валидация на формата
                const validationErrors = self.validateUserForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                // Показване на loading състояние
                const submitButton = form.querySelector('button[type="submit"]') || form.querySelector('#save-new-user');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const addUserUrl = self.settings.config.ajaxUrls.add_user ||
                                  window.location.pathname + '?route=setting/admin_users/add&user_token=' + self.settings.config.userToken;

                fetch(addUserUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    // Проверка дали отговорът е JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Сървърът не върна JSON отговор. Възможно е сесията да е изтекла.');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        // Презареждане на таблицата с потребители
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error adding user:', error);
                    self.showSettingsNotification('Възникна грешка при добавянето', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Валидация на формата за админ потребители
             */
            validateAdminUsersForm: function(form) {
                const errors = [];

                // Валидация на настройки за сигурност
                const minLength = form.querySelector('[name="security_settings[password_min_length]"]')?.value;
                if (minLength && (parseInt(minLength) < 6 || parseInt(minLength) > 32)) {
                    errors.push('Минималната дължина на паролата трябва да бъде между 6 и 32 символа');
                }

                const maxAttempts = form.querySelector('[name="security_settings[max_login_attempts]"]')?.value;
                if (maxAttempts && (parseInt(maxAttempts) < 3 || parseInt(maxAttempts) > 20)) {
                    errors.push('Максималните опити за вход трябва да бъдат между 3 и 20');
                }

                const sessionTimeout = form.querySelector('[name="security_settings[session_timeout]"]')?.value;
                if (sessionTimeout && (parseInt(sessionTimeout) < 300 || parseInt(sessionTimeout) > 86400)) {
                    errors.push('Timeout на сесията трябва да бъде между 300 и 86400 секунди');
                }

                // Валидация на IP whitelist ако е активиран
                const ipWhitelistEnabled = form.querySelector('[name="security_settings[ip_whitelist_enabled]"]')?.checked;
                const ipWhitelist = form.querySelector('[name="security_settings[ip_whitelist]"]')?.value;

                if (ipWhitelistEnabled && !ipWhitelist) {
                    errors.push('IP whitelist е активиран, но няма зададени IP адреси');
                } else if (ipWhitelist) {
                    const ips = ipWhitelist.split(',');
                    ips.forEach(ip => {
                        const trimmedIp = ip.trim();
                        if (trimmedIp && !self.isValidIP(trimmedIp)) {
                            errors.push(`Невалиден IP адрес: ${trimmedIp}`);
                        }
                    });
                }

                return errors;
            },

            /**
             * Обработка на формата за добавяне на група
             */
            submitAddGroupForm: function(form) {
                const self = this;
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Валидация на формата
                const validationErrors = self.validateGroupForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                // Показване на loading състояние
                const submitButton = form.querySelector('button[type="submit"]') || form.querySelector('#save-new-group');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const addGroupUrl = self.settings.config.ajaxUrls.add_group ||
                                   window.location.pathname + '?route=setting/admin_users/add_group&user_token=' + self.settings.config.userToken;

                fetch(addGroupUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        // Презареждане на таблицата с групи
                        self.refreshUserGroupsTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error adding group:', error);
                    self.showSettingsNotification('Възникна грешка при добавянето', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази';
                    }
                });
            },

            /**
             * Проверка за валиден IP адрес
             */
            isValidIP: function(ip) {
                const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                return ipRegex.test(ip);
            },

            /**
             * Валидация на формата за потребител
             */
            validateUserForm: function(form) {
                const errors = [];

                const username = form.querySelector('[name="username"]')?.value?.trim();
                const password = form.querySelector('[name="password"]')?.value;
                const email = form.querySelector('[name="email"]')?.value?.trim();
                const firstname = form.querySelector('[name="firstname"]')?.value?.trim();
                const lastname = form.querySelector('[name="lastname"]')?.value?.trim();

                if (!username || username.length < 3) {
                    errors.push('Потребителското име трябва да бъде поне 3 символа');
                }

                if (!password || password.length < 6) {
                    errors.push('Паролата трябва да бъде поне 6 символа');
                }

                if (!email || !email.includes('@')) {
                    errors.push('Моля въведете валиден email адрес');
                }

                if (!firstname) {
                    errors.push('Моля въведете име');
                }

                if (!lastname) {
                    errors.push('Моля въведете фамилия');
                }

                return errors;
            },

            /**
             * Валидация на формата за група
             */
            validateGroupForm: function(form) {
                const errors = [];

                const name = form.querySelector('[name="name"]')?.value?.trim();

                if (!name || name.length < 2) {
                    errors.push('Името на групата трябва да бъде поне 2 символа');
                }

                return errors;
            },

            /**
             * Валидация на формата за редактиране на потребител
             */
            validateEditUserForm: function(form) {
                const errors = [];

                const username = form.querySelector('[name="username"]')?.value?.trim();
                const email = form.querySelector('[name="email"]')?.value?.trim();
                const firstname = form.querySelector('[name="firstname"]')?.value?.trim();
                const lastname = form.querySelector('[name="lastname"]')?.value?.trim();
                const password = form.querySelector('[name="password"]')?.value;
                const confirmPassword = form.querySelector('[name="confirm_password"]')?.value;

                if (!username || username.length < 3) {
                    errors.push('Потребителското име трябва да бъде поне 3 символа');
                }

                if (!email || !email.includes('@')) {
                    errors.push('Моля въведете валиден email адрес');
                }

                if (!firstname) {
                    errors.push('Моля въведете име');
                }

                if (!lastname) {
                    errors.push('Моля въведете фамилия');
                }

                // Валидация на парола само ако е въведена
                if (password && password.length > 0) {
                    if (password.length < 6) {
                        errors.push('Паролата трябва да бъде поне 6 символа');
                    }

                    if (password !== confirmPassword) {
                        errors.push('Паролите не съвпадат');
                    }
                }

                return errors;
            },

            /**
             * Показване на modal за добавяне на потребител
             */
            showAddUserModal: function() {
                const self = this;

                // Създаване на modal за добавяне на потребител
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на потребител</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-user-form" class="p-4">
                            <div class="space-y-4">
                                <div>
                                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Потребителско име</label>
                                    <input type="text" id="username" name="username" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Парола</label>
                                    <input type="password" id="password" name="password" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Потвърдение на паролата</label>
                                    <input type="password" id="confirm_password" name="confirm_password" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input type="email" id="email" name="email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">Име</label>
                                    <input type="text" id="firstname" name="firstname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">Фамилия</label>
                                    <input type="text" id="lastname" name="lastname" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="user_group_id" class="block text-sm font-medium text-gray-700 mb-1">Група</label>
                                    <select id="user_group_id" name="user_group_id" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="">Изберете група</option>
                                    </select>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="status" name="status" value="1" checked
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="status" class="ml-2 block text-sm text-gray-900">Активен</label>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // Зареждане на потребителски групи
                const userGroupSelect = modal.querySelector('#user_group_id');
                self.loadUserGroups(userGroupSelect);

                // Добавяне на event listeners за модала
                self.addModalEventListeners(modal);
            },

            /**
             * Добавяне на event listeners за модали
             */
            addModalEventListeners: function(modal) {
                const self = this;

                // Event listener за затваряне на модала
                const closeButtons = modal.querySelectorAll('.close-modal');
                closeButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        modal.remove();
                    });
                });

                // Event listener за затваряне при клик извън модала
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });

                // Event listener за затваряне с ESC клавиш
                const handleEscKey = function(e) {
                    if (e.key === 'Escape') {
                        modal.remove();
                        document.removeEventListener('keydown', handleEscKey);
                    }
                };
                document.addEventListener('keydown', handleEscKey);

                // Event listener за form submit
                const form = modal.querySelector('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();

                        if (form.id === 'add-user-form') {
                            self.submitAddUserForm(form);
                        } else if (form.id === 'add-group-form') {
                            self.submitAddGroupForm(form);
                        } else if (form.id === 'edit-user-form') {
                            self.submitEditUserForm(form);
                        } else if (form.id === 'edit-group-form') {
                            self.submitEditGroupForm(form);
                        }
                    });
                }

                // Премахване на event listeners при премахване на модала
                const originalRemove = modal.remove;
                modal.remove = function() {
                    document.removeEventListener('keydown', handleEscKey);
                    originalRemove.call(modal);
                };
            },

            /**
             * Показване на modal за добавяне на група потребители
             */
            showAddUserGroupModal: function() {
                const self = this;
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Добавяне на група</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="add-group-form" class="p-4">
                            <div class="space-y-6">
                                <div class="space-y-4">
                                    <h4 class="text-md font-medium text-gray-900">Основна информация</h4>
                                    <div>
                                        <label for="group_name" class="block text-sm font-medium text-gray-700 mb-1">Име на групата <span class="text-red-500">*</span></label>
                                        <input type="text" id="group_name" name="name" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    </div>
                                    <div>
                                        <label for="group_description" class="block text-sm font-medium text-gray-700 mb-1">Описание</label>
                                        <textarea id="group_description" name="description" rows="3"
                                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"></textarea>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <h4 class="text-md font-medium text-gray-900">Права на достъп</h4>
                                    <div id="permissions-container" class="space-y-3">
                                        <div class="text-center py-4">
                                            <i class="ri-loader-4-line animate-spin text-xl text-gray-500"></i>
                                            <p class="text-sm text-gray-500 mt-2">Зареждане на права...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-new-group" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // Добавяне на event listeners за модала
                self.addModalEventListeners(modal);

                // Зареждане на правата
                self.loadPermissionsForGroup(modal.querySelector('#permissions-container'));
            },

            /**
             * Зареждане на права за група
             */
            loadPermissionsForGroup: function(container, selectedPermissions = {}) {
                const self = this;

                const getPermissionsUrl = (self.settings.config.ajaxUrls.get_available_permissions ?
                                         self.settings.config.ajaxUrls.get_available_permissions :
                                         window.location.pathname + '?route=setting/admin_users/get_available_permissions&user_token=' + self.settings.config.userToken);

                fetch(getPermissionsUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Сървърът не върна JSON отговор.');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.permissions) {
                        self.renderPermissions(container, data.permissions, selectedPermissions);
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-4 text-gray-500">
                                <i class="ri-information-line text-xl mb-2"></i>
                                <p>Няма налични права за конфигуриране</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading permissions:', error);
                    container.innerHTML = `
                        <div class="text-center py-4 text-red-500">
                            <i class="ri-error-warning-line text-xl mb-2"></i>
                            <p>Грешка при зареждане на правата</p>
                        </div>
                    `;
                });
            },

            /**
             * Зареждане на права за редактиране на група
             * Този метод използва вече заредените права на групата
             */
            loadPermissionsForEditGroup: function(container, groupPermissions = {}) {
                const self = this;



                // Показване на loading състояние
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="ri-loader-4-line animate-spin text-xl text-gray-500"></i>
                        <p class="text-sm text-gray-500 mt-2">Зареждане на права...</p>
                    </div>
                `;

                const getPermissionsUrl = (self.settings.config.ajaxUrls.get_available_permissions ?
                                         self.settings.config.ajaxUrls.get_available_permissions :
                                         window.location.pathname + '?route=setting/admin_users/get_available_permissions&user_token=' + self.settings.config.userToken);

                fetch(getPermissionsUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Сървърът не върна JSON отговор.');
                    }
                    return response.json();
                })
                .then(data => {

                    if (data.permissions) {
                        // Подаваме групата права като selectedPermissions
                        self.renderPermissions(container, data.permissions, groupPermissions);
                    } else {
                        container.innerHTML = `
                            <div class="text-center py-4 text-gray-500">
                                <i class="ri-information-line text-xl mb-2"></i>
                                <p>Няма налични права за конфигуриране</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading permissions:', error);
                    container.innerHTML = `
                        <div class="text-center py-4 text-red-500">
                            <i class="ri-error-warning-line text-xl mb-2"></i>
                            <p>Грешка при зареждане на правата</p>
                        </div>
                    `;
                });
            },

            /**
             * Рендиране на права в контейнер
             */
            renderPermissions: function(container, permissions, selectedPermissions = {}) {
                const self = this;


                console.log(selectedPermissions);


                if (!permissions || typeof permissions !== 'object') {
                    container.innerHTML = `
                        <div class="text-center py-4 text-gray-500">
                            <p>Няма налични права за конфигуриране</p>
                        </div>
                    `;
                    return;
                }

                // Структурираме правата в две секции
                const accessPermissions = permissions.access || {};
                const modifyPermissions = permissions.modify || {};

                let permissionsHtml = `
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h5 class="text-lg font-medium text-gray-900">Права за достъп</h5>
                                <div class="flex space-x-2">
                                    <button type="button" class="select-all-access text-sm px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200">
                                        Избери всички
                                    </button>
                                    <button type="button" class="unselect-all-access text-sm px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                        Премахни всички
                                    </button>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                                <div class="space-y-2">
                `;

                // Генериране на чекбоксове за access права
                Object.keys(accessPermissions).forEach(route => {
                    const displayName = accessPermissions[route];

                    // Проверка дали правото е избрано
                    let isSelected = false;
                    if (selectedPermissions && selectedPermissions.access) {
                        if (Array.isArray(selectedPermissions.access)) {
                            isSelected = selectedPermissions.access.includes(route);
                        } else if (typeof selectedPermissions.access === 'object') {
                            isSelected = selectedPermissions.access.hasOwnProperty(route);
                        }
                    }



                    permissionsHtml += `
                        <label class="flex items-center hover:bg-gray-50 p-2 rounded">
                            <input type="checkbox" name="permissions[access][]" value="${route}"
                                   ${isSelected ? 'checked' : ''}
                                   class="access-permission mr-3 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            <div class="flex-1">
                                <span class="text-sm font-medium text-gray-900">${displayName}</span>
                                <div class="text-xs text-gray-500">${route}</div>
                            </div>
                        </label>
                    `;
                });

                permissionsHtml += `
                                </div>
                            </div>
                        </div>

                        <!-- Права за редактиране -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h5 class="text-lg font-medium text-gray-900">Права за редактиране</h5>
                                <div class="flex space-x-2">
                                    <button type="button" class="select-all-modify text-sm px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200">
                                        Избери всички
                                    </button>
                                    <button type="button" class="unselect-all-modify text-sm px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                                        Премахни всички
                                    </button>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                                <div class="space-y-2">
                `;

                // Генериране на чекбоксове за modify права
                Object.keys(modifyPermissions).forEach(route => {
                    const displayName = modifyPermissions[route];

                    // Проверка дали правото е избрано
                    let isSelected = false;
                    if (selectedPermissions && selectedPermissions.modify) {
                        if (Array.isArray(selectedPermissions.modify)) {
                            isSelected = selectedPermissions.modify.includes(route);
                        } else if (typeof selectedPermissions.modify === 'object') {
                            isSelected = selectedPermissions.modify.hasOwnProperty(route);
                        }
                    }



                    permissionsHtml += `
                        <label class="flex items-center hover:bg-gray-50 p-2 rounded">
                            <input type="checkbox" name="permissions[modify][]" value="${route}"
                                   ${isSelected ? 'checked' : ''}
                                   class="modify-permission mr-3 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            <div class="flex-1">
                                <span class="text-sm font-medium text-gray-900">${displayName}</span>
                                <div class="text-xs text-gray-500">${route}</div>
                            </div>
                        </label>
                    `;
                });

                permissionsHtml += `
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                container.innerHTML = permissionsHtml;

                // Добавяне на event listeners за бутоните
                self.addPermissionButtonListeners(container);
            },

            /**
             * Добавяне на event listeners за бутоните за избор на права
             */
            addPermissionButtonListeners: function(container) {
                // Избери всички access права
                const selectAllAccess = container.querySelector('.select-all-access');
                if (selectAllAccess) {
                    selectAllAccess.addEventListener('click', function() {
                        const checkboxes = container.querySelectorAll('.access-permission');
                        checkboxes.forEach(cb => cb.checked = true);
                    });
                }

                // Премахни всички access права
                const unselectAllAccess = container.querySelector('.unselect-all-access');
                if (unselectAllAccess) {
                    unselectAllAccess.addEventListener('click', function() {
                        const checkboxes = container.querySelectorAll('.access-permission');
                        checkboxes.forEach(cb => cb.checked = false);
                    });
                }

                // Избери всички modify права
                const selectAllModify = container.querySelector('.select-all-modify');
                if (selectAllModify) {
                    selectAllModify.addEventListener('click', function() {
                        const checkboxes = container.querySelectorAll('.modify-permission');
                        checkboxes.forEach(cb => cb.checked = true);
                    });
                }

                // Премахни всички modify права
                const unselectAllModify = container.querySelector('.unselect-all-modify');
                if (unselectAllModify) {
                    unselectAllModify.addEventListener('click', function() {
                        const checkboxes = container.querySelectorAll('.modify-permission');
                        checkboxes.forEach(cb => cb.checked = false);
                    });
                }
            },

            /**
             * Редактиране на потребител
             */
            editUser: function(userId) {
                const self = this;

                // Първо зареждаме данните за потребителя
                const getUserUrl = (self.settings.config.ajaxUrls.get_user ?
                                   self.settings.config.ajaxUrls.get_user + '&user_id=' + userId :
                                   window.location.pathname + '?route=setting/admin_users/get_user&user_token=' + self.settings.config.userToken + '&user_id=' + userId);

                fetch(getUserUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        self.showEditUserModal(data.user);
                    } else {
                        self.showSettingsNotification('Грешка при зареждане на данните за потребителя', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading user data:', error);
                    self.showSettingsNotification('Грешка при зареждане на данните за потребителя', 'error');
                });
            },

            /**
             * Показване на modal за редактиране на потребител
             */
            showEditUserModal: function(userData) {
                const self = this;
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="flex justify-between items-center p-4 border-b">
                            <h3 class="text-lg font-semibold">Редактиране на потребител</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <form id="edit-user-form" class="p-4">
                            <input type="hidden" name="user_id" value="${userData.user_id}">
                            <div class="space-y-4">
                                <div>
                                    <label for="edit_username" class="block text-sm font-medium text-gray-700 mb-1">Потребителско име <span class="text-red-500">*</span></label>
                                    <input type="text" id="edit_username" name="username" required
                                           value="${userData.username || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="edit_email" class="block text-sm font-medium text-gray-700 mb-1">Email <span class="text-red-500">*</span></label>
                                    <input type="email" id="edit_email" name="email" required
                                           value="${userData.email || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="edit_firstname" class="block text-sm font-medium text-gray-700 mb-1">Име <span class="text-red-500">*</span></label>
                                    <input type="text" id="edit_firstname" name="firstname" required
                                           value="${userData.firstname || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="edit_lastname" class="block text-sm font-medium text-gray-700 mb-1">Фамилия <span class="text-red-500">*</span></label>
                                    <input type="text" id="edit_lastname" name="lastname" required
                                           value="${userData.lastname || ''}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                </div>
                                <div>
                                    <label for="edit_password" class="block text-sm font-medium text-gray-700 mb-1">Нова парола</label>
                                    <input type="password" id="edit_password" name="password"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                                           placeholder="Оставете празно за да не променяте">
                                    <p class="text-xs text-gray-500 mt-1">Минимум 6 символа</p>
                                </div>
                                <div>
                                    <label for="edit_confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Потвърждение на парола</label>
                                    <input type="password" id="edit_confirm_password" name="confirm_password"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                                           placeholder="Повторете новата парола">
                                </div>
                                <div>
                                    <label for="edit_user_group_id" class="block text-sm font-medium text-gray-700 mb-1">Група <span class="text-red-500">*</span></label>
                                    <select id="edit_user_group_id" name="user_group_id" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="">Зареждане...</option>
                                    </select>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="edit_status" name="status" value="1" ${userData.status == '1' ? 'checked' : ''}
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="edit_status" class="ml-2 block text-sm text-gray-900">Активен</label>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" class="close-modal px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-edit-user" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази промените
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // Добавяне на event listeners за модала
                self.addModalEventListeners(modal);

                // Зареждане на потребителските групи
                self.loadUserGroups(modal.querySelector('#edit_user_group_id'), userData.user_group_id);

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="username"]');
                if (firstInput) {
                    firstInput.focus();
                }
            },

            /**
             * Обработка на формата за редактиране на потребител
             */
            submitEditUserForm: function(form) {
                const self = this;
                const formData = new FormData(form);
                formData.append('user_token', self.settings.config.userToken);

                // Валидация на формата
                const validationErrors = self.validateEditUserForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                // Показване на loading състояние
                const submitButton = form.closest('.bg-white').querySelector('#save-edit-user');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const editUserUrl = self.settings.config.ajaxUrls.edit_user ||
                                   window.location.pathname + '?route=setting/admin_users/edit&user_token=' + self.settings.config.userToken;

                fetch(editUserUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    // Проверка дали отговорът е JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Сървърът не върна JSON отговор. Възможно е сесията да е изтекла.');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        // Презареждане на таблицата с потребители
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error editing user:', error);
                    self.showSettingsNotification('Възникна грешка при редактирането', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                    }
                });
            },


            /**
             * Изтриване на потребител
             */
            deleteUser: function(userId) {
                const self = this;

                // Показване на confirmation modal
                self.showDeleteConfirmModal('потребител', () => {
                    const formData = new FormData();
                    formData.append('user_token', self.settings.config.userToken);
                    formData.append('user_id', userId);

                    const deleteUserUrl = self.settings.config.ajaxUrls.delete_user ||
                                         window.location.pathname + '?route=setting/admin_users/delete&user_token=' + self.settings.config.userToken;

                    fetch(deleteUserUrl, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            self.showSettingsNotification(data.success, 'success');

                            // Презареждане на таблицата с потребители
                            self.refreshUsersTable();
                        } else if (data.error) {
                            self.showSettingsNotification(data.error, 'error');
                        }
                    })
                    .catch(error => {
                        self.logDev && self.logDev('Error deleting user:', error);
                        self.showSettingsNotification('Възникна грешка при изтриването', 'error');
                    });
                });
            },

            /**
             * Toggle на статуса на потребител
             */
            toggleUserStatus: function(userId) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                const toggleStatusUrl = (self.settings.config.ajaxUrls.toggle_user_status ?
                                       self.settings.config.ajaxUrls.toggle_user_status :
                                       window.location.pathname + '?route=setting/admin_users/toggle_status&user_token=' + self.settings.config.userToken);

                fetch(toggleStatusUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    // Проверка дали отговорът е JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('Сървърът не върна JSON отговор. Възможно е сесията да е изтекла.');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Вместо да презареждаме цялата таблица, ще актуализираме само статуса
                        if (data.new_status !== undefined) {
                            self.updateUserStatusInTable(userId, data.new_status);
                        } else {
                            // Fallback към презареждане на таблицата
                            self.refreshUsersTable();
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error toggling user status:', error);
                    self.showSettingsNotification('Възникна грешка при промяната', 'error');
                });
            },

            /**
             * Актуализиране на статуса на потребител в таблицата
             */
            updateUserStatusInTable: function(userId, newStatus) {
                const statusElement = document.querySelector(`[data-user-id="${userId}"] .user-status`);
                const toggleButton = document.querySelector(`[data-user-id="${userId}"] .toggle-user-status`);

                if (statusElement) {
                    if (newStatus == '1') {
                        statusElement.innerHTML = '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">Активен</span>';
                    } else {
                        statusElement.innerHTML = '<span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">Неактивен</span>';
                    }
                }

                if (toggleButton) {
                    if (newStatus == '1') {
                        toggleButton.innerHTML = '<i class="ri-pause-circle-line"></i> Деактивирай';
                        toggleButton.className = toggleButton.className.replace('bg-green-500', 'bg-red-500');
                    } else {
                        toggleButton.innerHTML = '<i class="ri-play-circle-line"></i> Активирай';
                        toggleButton.className = toggleButton.className.replace('bg-red-500', 'bg-green-500');
                    }
                }
            },

            /**
             * Инициализиране на таблицата с потребители
             */
            initUserTable: function() {
                // Инициализиране на функционалност за таблицата с потребители
                this.logDev && this.logDev('User table initialized');
            },

            /**
             * Инициализиране на таблицата с групи потребители
             */
            initUserGroupsTable: function() {
                // Инициализиране на функционалност за таблицата с групи
                this.logDev && this.logDev('User groups table initialized');
            },

            /**
             * Зареждане на потребителски групи в select поле
             */
            loadUserGroups: function(selectElement, selectedValue = null) {
                const self = this;

                if (!selectElement) {
                    self.logDev && self.logDev('Select element not found for user groups');
                    return;
                }

                // Показване на loading състояние
                selectElement.innerHTML = '<option value="">Зареждане...</option>';

                const getUserGroupsUrl = self.settings.config.ajaxUrls.get_user_groups ||
                                       window.location.pathname + '?route=setting/admin_users/get_user_groups&user_token=' + self.settings.config.userToken;

                fetch(getUserGroupsUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    selectElement.innerHTML = '';

                    if (data.groups && Array.isArray(data.groups)) {
                        data.groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.user_group_id;
                            option.textContent = group.name;
                            if (selectedValue && group.user_group_id == selectedValue) {
                                option.selected = true;
                            }
                            selectElement.appendChild(option);
                        });
                    } else {
                        selectElement.innerHTML = '<option value="">Няма налични групи</option>';
                    }
                })
                .catch(error => {
                    console.error('Error loading user groups:', error);
                    selectElement.innerHTML = '<option value="">Грешка при зареждане</option>';
                });
            },

            /**
             * Редактиране на група потребители
             */
            editUserGroup: function(groupId) {
                const self = this;

                // Първо зареждаме данните за групата
                const getGroupUrl = (self.settings.config.ajaxUrls.get_user_group ?
                                    self.settings.config.ajaxUrls.get_user_group + '&user_group_id=' + groupId :
                                    window.location.pathname + '?route=setting/admin_users/get_user_group&user_token=' + self.settings.config.userToken + '&user_group_id=' + groupId);



                fetch(getGroupUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {

                    console.log(data);

                    if (data.group) {
                        self.showEditUserGroupModal(data.group);
                    } else {
                        self.showSettingsNotification('Грешка при зареждане на данните за групата', 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error loading group data:', error);
                    self.showSettingsNotification('Грешка при зареждане на данните за групата', 'error');
                });
            },

            /**
             * Показване на modal за редактиране на група потребители
             */
            showEditUserGroupModal: function(groupData) {


                console.log(groupData);

                const self = this;
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                        <div class="flex justify-between items-center p-6 border-b">
                            <h3 class="text-xl font-semibold">Редактиране на група</h3>
                            <button class="close-modal text-gray-500 hover:text-gray-700">
                                <i class="ri-close-line text-2xl"></i>
                            </button>
                        </div>
                        <form id="edit-group-form" class="p-6">
                            <input type="hidden" name="user_group_id" value="${groupData.user_group_id}">
                            <div class="space-y-8">
                                <div class="space-y-4">
                                    <h4 class="text-lg font-medium text-gray-900">Основна информация</h4>
                                    <div class="grid grid-cols-1">
                                        <div>
                                            <label for="edit_group_name" class="block text-sm font-medium text-gray-700 mb-1">Име на групата <span class="text-red-500">*</span></label>
                                            <input type="text" id="edit_group_name" name="name" required
                                                   value="${groupData.name || ''}"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                        </div>
                                    </div>
                                </div>

                                <div class="space-y-4">
                                    <h4 class="text-lg font-medium text-gray-900">Права на достъп</h4>
                                    <div id="edit-permissions-container" class="space-y-4">
                                        <div class="text-center py-8">
                                            <i class="ri-loader-4-line animate-spin text-2xl text-gray-500"></i>
                                            <p class="text-sm text-gray-500 mt-2">Зареждане на права...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3 mt-8 pt-6 border-t">
                                <button type="button" class="close-modal px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button type="submit" id="save-edit-group" class="px-6 py-2 bg-primary text-white rounded hover:bg-primary/90">
                                    <i class="ri-save-line mr-2"></i>Запази промените
                                </button>
                            </div>
                        </form>
                    </div>
                `;

                document.body.appendChild(modal);

                // Добавяне на event listeners за модала
                self.addModalEventListeners(modal);

                // Зареждане на правата за групата
                self.loadPermissionsForEditGroup(
                    modal.querySelector('#edit-permissions-container'),
                    groupData.permission || {}
                );

                // Фокус на първото поле
                const firstInput = modal.querySelector('input[name="name"]');
                if (firstInput) {
                    firstInput.focus();
                }
            },

            /**
             * Обработка на формата за редактиране на група
             */
            submitEditGroupForm: function(form) {
                const self = this;

                // Валидация на формата
                const validationErrors = self.validateGroupForm(form);
                if (validationErrors.length > 0) {
                    validationErrors.forEach(error => {
                        self.showSettingsNotification(error, 'error');
                    });
                    return;
                }

                // Събиране на данните от формата
                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_group_id', form.querySelector('[name="user_group_id"]').value);
                formData.append('name', form.querySelector('[name="name"]').value);
                // formData.append('description', form.querySelector('[name="description"]').value);

                // Събиране на правата
                const permissions = {
                    access: [],
                    modify: []
                };

                // Събиране на access права
                const accessCheckboxes = form.querySelectorAll('input[name="permissions[access][]"]:checked');
                accessCheckboxes.forEach(checkbox => {
                    permissions.access.push(checkbox.value);
                });

                // Събиране на modify права
                const modifyCheckboxes = form.querySelectorAll('input[name="permissions[modify][]"]:checked');
                modifyCheckboxes.forEach(checkbox => {
                    permissions.modify.push(checkbox.value);
                });

                // Добавяне на правата към formData
                formData.append('permissions', JSON.stringify(permissions));

                // Показване на loading състояние
                const submitButton = form.closest('.bg-white').querySelector('#save-edit-group');
                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }

                const editGroupUrl = self.settings.config.ajaxUrls.edit_group ||
                                   window.location.pathname + '?route=setting/admin_users/edit_group&user_token=' + self.settings.config.userToken;

                fetch(editGroupUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Затваряне на модала
                        const modal = form.closest('.fixed.inset-0');
                        if (modal) {
                            modal.remove();
                        }

                        // Презареждане на таблицата с групи
                        self.refreshUserGroupsTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');

                        // Показване на грешки при валидация
                        if (data.validation_errors) {
                            self.showValidationErrors(data.validation_errors);
                        }
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error editing group:', error);
                    self.showSettingsNotification('Възникна грешка при редактирането', 'error');
                })
                .finally(() => {
                    // Възстановяване на бутона
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                    }
                });
            },

            /**
             * Изтриване на група потребители
             */
            deleteUserGroup: function(groupId) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да изтриете тази група?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_group_id', groupId);

                fetch(self.settings.config.ajaxUrls.delete_user_group || '', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUserGroupsTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error deleting user group:', error);
                    self.showSettingsNotification('Възникна грешка при изтриването', 'error');
                });
            },

            /**
             * Управление на права на достъп (пренасочва към редактиране на група)
             */
            managePermissions: function(groupId) {
                // Пренасочваме към редактиране на групата където правата са интегрирани
                this.editUserGroup(groupId);
            },





            /**
             * Презареждане на таблицата с потребители
             */
            refreshUsersTable: function() {
                const self = this;
                const usersTableContainer = document.getElementById('users-table-container');

                if (!usersTableContainer) {
                    self.logDev && self.logDev('Users table container not found');
                    return;
                }

                // Показване на loading състояние
                usersTableContainer.innerHTML = `
                    <div class="flex justify-center items-center py-8">
                        <i class="ri-loader-4-line animate-spin text-2xl text-gray-500 mr-2"></i>
                        <span class="text-gray-500">Зареждане...</span>
                    </div>
                `;

                const getUsersUrl = self.settings.config.ajaxUrls.get_users ||
                                  window.location.pathname + '?route=setting/admin_users/get_users&user_token=' + self.settings.config.userToken;

                fetch(getUsersUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        usersTableContainer.innerHTML = data.html;
                    } else if (data.error) {
                        usersTableContainer.innerHTML = `
                            <div class="text-center py-8 text-red-500">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error refreshing users table:', error);
                    usersTableContainer.innerHTML = `
                        <div class="text-center py-8 text-red-500">
                            <i class="ri-error-warning-line text-2xl mb-2"></i>
                            <p>Грешка при зареждане на данните</p>
                        </div>
                    `;
                });
            },

            /**
             * Презареждане на таблицата с групи потребители
             */
            refreshUserGroupsTable: function() {
                const self = this;
                const groupsTableContainer = document.getElementById('user-groups-table-container');

                if (!groupsTableContainer) {
                    self.logDev && self.logDev('User groups table container not found');
                    return;
                }

                // Показване на loading състояние
                groupsTableContainer.innerHTML = `
                    <div class="flex justify-center items-center py-8">
                        <i class="ri-loader-4-line animate-spin text-2xl text-gray-500 mr-2"></i>
                        <span class="text-gray-500">Зареждане...</span>
                    </div>
                `;

                const getGroupsUrl = self.settings.config.ajaxUrls.get_user_groups ||
                                   window.location.pathname + '?route=setting/admin_users/get_user_groups&user_token=' + self.settings.config.userToken;

                fetch(getGroupsUrl, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.html) {
                        groupsTableContainer.innerHTML = data.html;
                    } else if (data.error) {
                        groupsTableContainer.innerHTML = `
                            <div class="text-center py-8 text-red-500">
                                <i class="ri-error-warning-line text-2xl mb-2"></i>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error refreshing user groups table:', error);
                    groupsTableContainer.innerHTML = `
                        <div class="text-center py-8 text-red-500">
                            <i class="ri-error-warning-line text-2xl mb-2"></i>
                            <p>Грешка при зареждане на данните</p>
                        </div>
                    `;
                });
            },

            /**
             * Презареждане на логовете за активност
             */
            refreshActivityLogs: function() {
                // Презареждане на логовете
                this.logDev && this.logDev('Refreshing activity logs');
                // Тук ще се имплементира презареждането на логовете
            },

            /**
             * Възстановяване на парола на потребител
             */
            resetUserPassword: function(userId) {
                const self = this;

                if (!self.showSettingsConfirm('Сигурни ли сте, че искате да възстановите паролата на този потребител?')) {
                    return;
                }

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                const resetPasswordUrl = self.settings.config.ajaxUrls.reset_password ||
                                       window.location.pathname + '?route=setting/admin_users/reset_password&user_token=' + self.settings.config.userToken;

                fetch(resetPasswordUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');

                        // Показване на новата парола ако е предоставена
                        if (data.new_password) {
                            self.showSettingsNotification('Новата парола е: ' + data.new_password, 'info');
                        }
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error resetting password:', error);
                    self.showSettingsNotification('Възникна грешка при възстановяването', 'error');
                });
            },

            /**
             * Отключване на потребител
             */
            unlockUser: function(userId) {
                const self = this;

                const formData = new FormData();
                formData.append('user_token', self.settings.config.userToken);
                formData.append('user_id', userId);

                const unlockUserUrl = self.settings.config.ajaxUrls.unlock_user ||
                                    window.location.pathname + '?route=setting/admin_users/unlock&user_token=' + self.settings.config.userToken;

                fetch(unlockUserUrl, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        self.showSettingsNotification(data.success, 'success');
                        self.refreshUsersTable();
                    } else if (data.error) {
                        self.showSettingsNotification(data.error, 'error');
                    }
                })
                .catch(error => {
                    self.logDev && self.logDev('Error unlocking user:', error);
                    self.showSettingsNotification('Възникна грешка при отключването', 'error');
                });
            },

            /**
             * Показване на confirmation modal за изтриване
             */
            showDeleteConfirmModal: function(itemType, onConfirm) {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg max-w-md w-full mx-4">
                        <div class="p-6">
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0">
                                    <i class="ri-error-warning-line text-3xl text-red-500"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Потвърждение за изтриване</h3>
                                    <p class="text-sm text-gray-500">Сигурни ли сте, че искате да изтриете този ${itemType}?</p>
                                </div>
                            </div>
                            <div class="flex justify-end space-x-3">
                                <button class="cancel-delete px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                    Отказ
                                </button>
                                <button class="confirm-delete px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                                    Изтрий
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Event listeners
                modal.querySelector('.cancel-delete').addEventListener('click', () => {
                    modal.remove();
                });

                modal.querySelector('.confirm-delete').addEventListener('click', () => {
                    modal.remove();
                    if (typeof onConfirm === 'function') {
                        onConfirm();
                    }
                });

                // Затваряне при клик извън модала
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
            },

            /**
             * Показване на потвърждение за настройки
             */
            showSettingsConfirm: function(message) {
                return confirm(message);
            },

            /**
             * Показване на грешки при валидация
             */
            showValidationErrors: function(errors) {
                // Изчистване на предишни грешки
                const errorElements = document.querySelectorAll('.field-error');
                errorElements.forEach(el => el.remove());

                // Показване на нови грешки
                Object.keys(errors).forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'field-error text-red-500 text-sm mt-1';
                        errorDiv.textContent = errors[fieldName];
                        field.parentNode.appendChild(errorDiv);
                    }
                });
            },

            /**
             * Автоматично запазване на настройки
             */
            autoSaveSettings: function() {
                const self = this;
                // Debounce за автоматично запазване
                clearTimeout(self.autoSaveTimeout);
                self.autoSaveTimeout = setTimeout(() => {
                    self.logDev && self.logDev('Auto-saving settings...');
                    // Тук може да се добави логика за автоматично запазване
                }, 1000);
            },

            /**
             * Toggle на IP whitelist полета
             */
            toggleIPWhitelistFields: function(enabled) {
                const ipWhitelistContainer = document.getElementById('ip-whitelist-container');
                if (ipWhitelistContainer) {
                    if (enabled) {
                        ipWhitelistContainer.style.display = 'block';
                    } else {
                        ipWhitelistContainer.style.display = 'none';
                    }
                }
            },

            /**
             * Актуализиране на показването на настройки за админ потребители
             */
            updateAdminUsersSettingsDisplay: function(data) {
                // Актуализиране на статистики ако има
                if (data.statistics) {
                    const totalUsersElement = document.getElementById('total-users-count');
                    if (totalUsersElement && data.statistics.total_users !== undefined) {
                        totalUsersElement.textContent = data.statistics.total_users;
                    }

                    const activeUsersElement = document.getElementById('active-users-count');
                    if (activeUsersElement && data.statistics.active_users !== undefined) {
                        activeUsersElement.textContent = data.statistics.active_users;
                    }

                    const totalGroupsElement = document.getElementById('total-groups-count');
                    if (totalGroupsElement && data.statistics.total_groups !== undefined) {
                        totalGroupsElement.textContent = data.statistics.total_groups;
                    }
                }

                // Актуализиране на настройки ако има
                if (data.settings) {
                    Object.keys(data.settings).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            if (field.type === 'checkbox') {
                                field.checked = data.settings[key] == '1';
                            } else {
                                field.value = data.settings[key];
                            }
                        }
                    });
                }

                // Актуализиране на потребители ако има промени
                if (data.users) {
                    this.refreshUsersTable();
                }

                // Актуализиране на групи потребители ако има промени
                if (data.user_groups) {
                    this.refreshUserGroupsTable();
                }
            }
        });
    }

    // Инициализация при зареждане на DOM
    document.addEventListener('DOMContentLoaded', function() {
        initAdminUsersModule();
    });

})();
