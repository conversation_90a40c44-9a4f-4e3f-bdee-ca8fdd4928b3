<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за AJAX актуализиране на настройки за сигурност
 */
class SecurityUpdate extends \Theme25\ControllerSubMethods {

    /**
     * Изпълнява AJAX актуализиране на настройки за сигурност
     */
    public function execute() {
        $json = [];

        // Проверка за права на достъп
        if (!$this->hasPermission('modify', 'setting/setting')) {
            $json['error'] = 'Нямате права за редактиране на настройки за сигурност';
            $this->outputJson($json);
            return;
        }

        // Проверка за POST заявка
        if (!$this->isPostRequest()) {
            $json['error'] = 'Невалидна заявка';
            $this->outputJson($json);
            return;
        }

        try {
            // Получаване на данните от POST
            $postData = $this->requestPost();
            
            // Валидация на данните
            $validation_errors = $this->validateSecuritySettings($postData);
            if (!empty($validation_errors)) {
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $validation_errors;
                $this->outputJson($json);
                return;
            }

            $this->loadModelAs('setting/security', 'securitySettings');

            // Запазване на настройките
            $result = $this->securitySettings->saveSettings($postData);
            
            if ($result === true) {
                $json['success'] = 'Настройките за сигурност са запазени успешно';
                $json['timestamp'] = date('d.m.Y H:i:s');
                
                // Добавяне на актуализирани данни
                $json['updated_data'] = $this->getUpdatedSecurityData($postData);
                
                // Предупреждения ако има
                $warnings = $this->checkSecurityWarnings($postData);
                if (!empty($warnings)) {
                    $json['warnings'] = $warnings;
                }
            } else {
                // Има грешки при валидацията от модела
                $json['error'] = 'Грешки при валидацията';
                $json['validation_errors'] = $result;
            }

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->outputJson($json);
    }

    /**
     * Валидира настройките за сигурност
     *
     * @param array $data Данни за валидация
     * @return array Масив с грешки
     */
    private function validateSecuritySettings($data) {
        $errors = [];

        // Валидация на IP адреси
        if (!empty($data['allowed_ips_text'])) {
            $ips = array_filter(array_map('trim', explode("\n", $data['allowed_ips_text'])));
            foreach ($ips as $ip) {
                if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                    $errors['allowed_ips_text'] = 'Невалиден IP адрес: ' . $ip;
                    break;
                }
            }
        }

        // Валидация на timeout на сесия
        if (isset($data['session_timeout'])) {
            $timeout = (int)$data['session_timeout'];
            if ($timeout < 300 || $timeout > 86400) {
                $errors['session_timeout'] = 'Timeout на сесията трябва да бъде между 5 минути и 24 часа';
            }
        }

        // Валидация на минимална дължина на паролата
        if (isset($data['password_min_length'])) {
            $length = (int)$data['password_min_length'];
            if ($length < 6 || $length > 20) {
                $errors['password_min_length'] = 'Минималната дължина на паролата трябва да бъде между 6 и 20 символа';
            }
        }

        // Валидация на лимит за опити за вход
        if (isset($data['login_attempts_limit'])) {
            $limit = (int)$data['login_attempts_limit'];
            if ($limit < 0 || $limit > 50) {
                $errors['login_attempts_limit'] = 'Лимитът за опити за вход трябва да бъде между 0 и 50';
            }
        }

        // Валидация на продължителност на блокиране
        if (isset($data['login_lockout_duration'])) {
            $duration = (int)$data['login_lockout_duration'];
            if ($duration < 60 || $duration > 86400) {
                $errors['login_lockout_duration'] = 'Продължителността на блокиране трябва да бъде между 1 минута и 24 часа';
            }
        }

        return $errors;
    }

    /**
     * Проверява за предупреждения относно сигурността
     *
     * @param array $data Данни за проверка
     * @return array Масив с предупреждения
     */
    private function checkSecurityWarnings($data) {
        $warnings = [];
        $current_ip = $this->request->server['REMOTE_ADDR'] ?? '';

        // Предупреждение за IP ограничения
        if (!empty($data['ip_restriction_enabled']) && !empty($data['allowed_ips_text'])) {
            $allowed_ips = array_filter(array_map('trim', explode("\n", $data['allowed_ips_text'])));
            if (!in_array($current_ip, $allowed_ips)) {
                $warnings[] = [
                    'type' => 'warning',
                    'message' => 'Вашият текущ IP адрес (' . $current_ip . ') не е в списъка с разрешени IP адреси. Може да загубите достъп до административния панел!'
                ];
            }
        }

        // Предупреждение за слаба политика за пароли
        if (empty($data['password_require_uppercase']) && 
            empty($data['password_require_lowercase']) && 
            empty($data['password_require_numbers']) && 
            empty($data['password_require_symbols'])) {
            $warnings[] = [
                'type' => 'info',
                'message' => 'Препоръчваме да активирате поне едно изискване за сложност на паролите'
            ];
        }



        // Предупреждение за SSL
        if (empty($data['force_ssl']) && !isset($_SERVER['HTTPS'])) {
            $warnings[] = [
                'type' => 'warning',
                'message' => 'Препоръчваме да активирате принудителното използване на SSL (HTTPS)'
            ];
        }

        return $warnings;
    }

    /**
     * Получава актуализираните данни за настройките за сигурност
     *
     * @param array $postData POST данни
     * @return array
     */
    private function getUpdatedSecurityData($postData) {
        // Обработка на IP адресите
        $allowed_ips = [];
        if (!empty($postData['allowed_ips_text'])) {
            $allowed_ips = array_filter(array_map('trim', explode("\n", $postData['allowed_ips_text'])));
        }

        return [
            'ip_blocking_enabled' => isset($postData['ip_restriction_enabled']) ? 1 : 0,
            'ip_whitelist' => implode("\n", $allowed_ips),
            'session_timeout' => (int)($postData['session_timeout'] ?? 3600),
            'force_ssl' => isset($postData['force_ssl']) ? 1 : 0,

            'password_min_length' => (int)($postData['password_min_length'] ?? 8),
            'password_require_uppercase' => isset($postData['password_require_uppercase']) ? 1 : 0,
            'password_require_lowercase' => isset($postData['password_require_lowercase']) ? 1 : 0,
            'password_require_numbers' => isset($postData['password_require_numbers']) ? 1 : 0,
            'password_require_symbols' => isset($postData['password_require_symbols']) ? 1 : 0,
            'login_attempts_limit' => (int)($postData['login_attempts_limit'] ?? 5),
            'login_lockout_duration' => (int)($postData['login_lockout_duration'] ?? 900),
            'admin_activity_log' => isset($postData['admin_activity_log']) ? 1 : 0,
            'failed_login_log' => isset($postData['failed_login_log']) ? 1 : 0
        ];
    }

    /**
     * Изпраща JSON отговор
     *
     * @param array $data
     */
    private function outputJson($data) {
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }

    /**
     * Логира действието за запазване на настройки за сигурност
     *
     * @param array $data Запазените данни
     */
    private function logSecuritySettingsSave($data) {
        try {
            $log_data = [
                'action' => 'security_settings_save',
                'user_id' => $this->user->getId(),
                'user_name' => $this->user->getUserName(),
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => $this->request->server['REMOTE_ADDR'] ?? 'unknown',
                'changed_fields' => array_keys($data),
                'critical_changes' => $this->identifyCriticalChanges($data)
            ];
            
            // Логиране в системния лог
            $this->log->write('Security Settings: Updated by user ' . $this->user->getUserName() . ' (' . $this->user->getId() . ')');
            
            // Ако има критични промени, логираме ги отделно
            if (!empty($log_data['critical_changes'])) {
                $this->log->write('Security Settings: Critical changes made - ' . implode(', ', $log_data['critical_changes']));
            }
            
        } catch (Exception $e) {
            // Ако логирането се провали, не спираме операцията
            error_log('Failed to log security settings save: ' . $e->getMessage());
        }
    }

    /**
     * Идентифицира критичните промени в настройките за сигурност
     *
     * @param array $data Данни за проверка
     * @return array Масив с критични промени
     */
    private function identifyCriticalChanges($data) {
        $critical_changes = [];

        // IP ограничения
        if (isset($data['ip_restriction_enabled'])) {
            $critical_changes[] = 'IP restrictions';
        }

        // SSL принуждаване
        if (isset($data['force_ssl'])) {
            $critical_changes[] = 'SSL enforcement';
        }



        // Лимит за опити за вход
        if (isset($data['login_attempts_limit'])) {
            $critical_changes[] = 'Login attempts limit';
        }

        return $critical_changes;
    }
}
